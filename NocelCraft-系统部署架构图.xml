<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2351.6484375 720.3863525390625" style="max-width: 2351.6484375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-8f76b448-2268-4347-8033-502a847e12d6"><style>#mermaid-8f76b448-2268-4347-8033-502a847e12d6{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .error-icon{fill:#a44141;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edge-thickness-normal{stroke-width:1px;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .marker.cross{stroke:lightgrey;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 p{margin:0;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .cluster-label text{fill:#F9FFFE;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .cluster-label span{color:#F9FFFE;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .cluster-label span p{background-color:transparent;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .label text,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 span{fill:#ccc;color:#ccc;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .node rect,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .node circle,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .node ellipse,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .node polygon,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .rough-node .label text,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .node .label text,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .image-shape .label,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .icon-shape .label{text-anchor:middle;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .rough-node .label,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .node .label,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .image-shape .label,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .icon-shape .label{text-align:center;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .node.clickable{cursor:pointer;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .arrowheadPath{fill:lightgrey;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .cluster text{fill:#F9FFFE;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .cluster span{color:#F9FFFE;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 rect.text{fill:none;stroke-width:0;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .icon-shape,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .icon-shape p,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .icon-shape rect,#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .clientClass&gt;*{fill:#e3f2fd!important;stroke:#1565c0!important;stroke-width:3px!important;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .clientClass span{fill:#e3f2fd!important;stroke:#1565c0!important;stroke-width:3px!important;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .localClass&gt;*{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:2px!important;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .localClass span{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:2px!important;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .cloudClass&gt;*{fill:#fff3e0!important;stroke:#ef6c00!important;stroke-width:2px!important;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .cloudClass span{fill:#fff3e0!important;stroke:#ef6c00!important;stroke-width:2px!important;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .aiClass&gt;*{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .aiClass span{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edgeClass&gt;*{fill:#ffebee!important;stroke:#c62828!important;stroke-width:2px!important;}#mermaid-8f76b448-2268-4347-8033-502a847e12d6 .edgeClass span{fill:#ffebee!important;stroke:#c62828!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph6" class="cluster"><rect height="128" width="411.09375" y="356.38636779785156" x="630.9921836853027" style=""></rect><g transform="translate(738.3359336853027, 356.38636779785156)" class="cluster-label"><foreignObject height="24" width="196.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>边缘计算 (Edge Computing)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="178" width="2301.2786388397217" y="534.3863677978516" x="42.369789123535156" style=""></rect><g transform="translate(1100.962233543396, 534.3863677978516)" class="cluster-label"><foreignObject height="24" width="184.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>云端服务 (Cloud Services)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="323.38636779785156" width="472.9036407470703" y="161" x="8" style=""></rect><g transform="translate(152.85807037353516, 161)" class="cluster-label"><foreignObject height="24" width="183.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>本地网络 (Local Network)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="298.38636779785156" width="1785.2187404632568" y="8" x="500.9036407470703" style=""></rect><g transform="translate(1305.690089225769, 8)" class="cluster-label"><foreignObject height="24" width="175.64584350585938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户设备 (Client Device)</p></span></div></foreignObject></g></g><g data-look="classic" id="基础设施服务" class="cluster"><rect height="128" width="574.6979064941406" y="559.3863677978516" x="1748.9505214691162" style=""></rect><g transform="translate(1988.2994747161865, 559.3863677978516)" class="cluster-label"><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>基础设施服务</p></span></div></foreignObject></g></g><g data-look="classic" id="数据存储服务" class="cluster"><rect height="128" width="902.6822853088379" y="559.3863677978516" x="62.369789123535156" style=""></rect><g transform="translate(465.7109317779541, 559.3863677978516)" class="cluster-label"><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据存储服务</p></span></div></foreignObject></g></g><g data-look="classic" id="AI服务提供商" class="cluster"><rect height="128" width="725.0468864440918" y="559.3863677978516" x="1003.9036350250244" style=""></rect><g transform="translate(1319.479160308838, 559.3863677978516)" class="cluster-label"><foreignObject height="24" width="93.89583587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AI服务提供商</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_LOCAL_DB_0" d="M1311.971,80.177L1195.141,89.481C1078.312,98.785,844.652,117.392,727.822,130.863C610.992,144.333,610.992,152.667,610.992,160.333C610.992,168,610.992,175,610.992,178.5L610.992,182"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_LOCAL_CACHE_1" d="M1311.971,85.576L1248.41,93.98C1184.848,102.384,1057.725,119.192,994.163,131.763C930.602,144.333,930.602,152.667,930.602,160.612C930.602,168.557,930.602,176.114,930.602,179.893L930.602,183.671"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_LOCAL_AI_2" d="M1311.971,99.401L1289.114,105.501C1266.257,111.601,1220.543,123.8,1197.685,134.067C1174.828,144.333,1174.828,152.667,1174.828,161.782C1174.828,170.898,1174.828,180.795,1174.828,185.744L1174.828,190.693"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_ROUTER_3" d="M1311.971,106.982L1297.776,111.819C1283.582,116.655,1255.192,126.327,1240.997,135.33C1226.802,144.333,1226.802,152.667,1086.25,167.825C945.698,182.984,664.594,204.969,524.042,215.961L383.49,226.953"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ROUTER_NAS_4" d="M235.487,260.693L218.051,268.309C200.614,275.924,165.742,291.155,148.306,302.938C130.87,314.72,130.87,323.053,130.87,331.386C130.87,339.72,130.87,348.053,130.87,355.72C130.87,363.386,130.87,370.386,130.87,373.886L130.87,377.386"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ROUTER_MONGODB_5" d="M286.162,260.693L283.019,268.309C279.876,275.924,273.59,291.155,270.448,302.938C267.305,314.72,267.305,323.053,267.305,331.386C267.305,339.72,267.305,348.053,267.305,362.886C267.305,377.72,267.305,399.053,267.305,420.386C267.305,441.72,267.305,463.053,267.305,477.886C267.305,492.72,267.305,501.053,267.305,509.386C267.305,517.72,267.305,526.053,267.305,534.386C267.305,542.72,267.305,551.053,320.846,563.64C374.388,576.227,481.471,593.068,535.012,601.489L588.554,609.909"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ROUTER_S3_6" d="M293.59,260.693L292.543,268.309C291.495,275.924,289.4,291.155,288.352,302.938C287.305,314.72,287.305,323.053,287.305,331.386C287.305,339.72,287.305,348.053,287.305,362.886C287.305,377.72,287.305,399.053,287.305,420.386C287.305,441.72,287.305,463.053,287.305,477.886C287.305,492.72,287.305,501.053,287.305,509.386C287.305,517.72,287.305,526.053,287.305,534.386C287.305,542.72,287.305,551.053,282.303,558.985C277.302,566.918,267.3,574.449,262.298,578.215L257.297,581.98"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ROUTER_CDN_7" d="M332.59,260.693L342.542,268.309C352.495,275.924,372.4,291.155,382.352,302.938C392.305,314.72,392.305,323.053,392.305,331.386C392.305,339.72,392.305,348.053,392.305,362.886C392.305,377.72,392.305,399.053,392.305,420.386C392.305,441.72,392.305,463.053,392.305,477.886C392.305,492.72,392.305,501.053,392.305,509.386C392.305,517.72,392.305,526.053,392.305,534.386C392.305,542.72,392.305,551.053,392.305,558.72C392.305,566.386,392.305,573.386,392.305,576.886L392.305,580.386"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_ZHIPU_8" d="M1331.851,111L1323.005,115.167C1314.159,119.333,1296.468,127.667,1287.622,136C1278.776,144.333,1278.776,152.667,1278.776,168.949C1278.776,185.231,1278.776,209.462,1278.776,233.693C1278.776,257.924,1278.776,282.155,1278.776,298.438C1278.776,314.72,1278.776,323.053,1278.776,331.386C1278.776,339.72,1278.776,348.053,1278.776,362.886C1278.776,377.72,1278.776,399.053,1278.776,420.386C1278.776,441.72,1278.776,463.053,1278.776,477.886C1278.776,492.72,1278.776,501.053,1278.776,509.386C1278.776,517.72,1278.776,526.053,1278.776,534.386C1278.776,542.72,1278.776,551.053,1261.212,562.156C1243.647,573.26,1208.518,587.133,1190.954,594.07L1173.39,601.006"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_SILICON_9" d="M1389.026,111L1386.289,115.167C1383.551,119.333,1378.076,127.667,1375.339,136C1372.602,144.333,1372.602,152.667,1372.602,168.949C1372.602,185.231,1372.602,209.462,1372.602,233.693C1372.602,257.924,1372.602,282.155,1372.602,298.438C1372.602,314.72,1372.602,323.053,1372.602,331.386C1372.602,339.72,1372.602,348.053,1372.602,362.886C1372.602,377.72,1372.602,399.053,1372.602,420.386C1372.602,441.72,1372.602,463.053,1372.602,477.886C1372.602,492.72,1372.602,501.053,1372.602,509.386C1372.602,517.72,1372.602,526.053,1372.602,534.386C1372.602,542.72,1372.602,551.053,1367.397,558.995C1362.193,566.937,1351.784,574.487,1346.579,578.262L1341.375,582.038"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_GOOGLE_10" d="M1449.462,111L1453.182,115.167C1456.901,119.333,1464.34,127.667,1468.059,136C1471.779,144.333,1471.779,152.667,1471.779,168.949C1471.779,185.231,1471.779,209.462,1471.779,233.693C1471.779,257.924,1471.779,282.155,1471.779,298.438C1471.779,314.72,1471.779,323.053,1471.779,331.386C1471.779,339.72,1471.779,348.053,1471.779,362.886C1471.779,377.72,1471.779,399.053,1471.779,420.386C1471.779,441.72,1471.779,463.053,1471.779,477.886C1471.779,492.72,1471.779,501.053,1471.779,509.386C1471.779,517.72,1471.779,526.053,1471.779,534.386C1471.779,542.72,1471.779,551.053,1471.779,558.72C1471.779,566.386,1471.779,573.386,1471.779,576.886L1471.779,580.386"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_GROK_11" d="M1517.326,101.201L1537.72,107C1558.114,112.8,1598.902,124.4,1619.296,134.367C1639.69,144.333,1639.69,152.667,1639.69,168.949C1639.69,185.231,1639.69,209.462,1639.69,233.693C1639.69,257.924,1639.69,282.155,1639.69,298.438C1639.69,314.72,1639.69,323.053,1639.69,331.386C1639.69,339.72,1639.69,348.053,1639.69,362.886C1639.69,377.72,1639.69,399.053,1639.69,420.386C1639.69,441.72,1639.69,463.053,1639.69,477.886C1639.69,492.72,1639.69,501.053,1639.69,509.386C1639.69,517.72,1639.69,526.053,1639.69,534.386C1639.69,542.72,1639.69,551.053,1639.69,558.72C1639.69,566.386,1639.69,573.386,1639.69,576.886L1639.69,580.386"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_AUTH_12" d="M1517.326,87.104L1572.724,95.253C1628.122,103.403,1738.919,119.701,1794.318,132.017C1849.716,144.333,1849.716,152.667,1849.716,168.949C1849.716,185.231,1849.716,209.462,1849.716,233.693C1849.716,257.924,1849.716,282.155,1849.716,298.438C1849.716,314.72,1849.716,323.053,1849.716,331.386C1849.716,339.72,1849.716,348.053,1849.716,362.886C1849.716,377.72,1849.716,399.053,1849.716,420.386C1849.716,441.72,1849.716,463.053,1849.716,477.886C1849.716,492.72,1849.716,501.053,1849.716,509.386C1849.716,517.72,1849.716,526.053,1849.716,534.386C1849.716,542.72,1849.716,551.053,1849.716,558.72C1849.716,566.386,1849.716,573.386,1849.716,576.886L1849.716,580.386"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_MONITOR_13" d="M1517.326,82.551L1604.02,91.459C1690.714,100.367,1864.103,118.184,1950.798,131.258C2037.492,144.333,2037.492,152.667,2037.492,168.949C2037.492,185.231,2037.492,209.462,2037.492,233.693C2037.492,257.924,2037.492,282.155,2037.492,298.438C2037.492,314.72,2037.492,323.053,2037.492,331.386C2037.492,339.72,2037.492,348.053,2037.492,362.886C2037.492,377.72,2037.492,399.053,2037.492,420.386C2037.492,441.72,2037.492,463.053,2037.492,477.886C2037.492,492.72,2037.492,501.053,2037.492,509.386C2037.492,517.72,2037.492,526.053,2037.492,534.386C2037.492,542.72,2037.492,551.053,2037.492,558.72C2037.492,566.386,2037.492,573.386,2037.492,576.886L2037.492,580.386"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLIENT_LOG_14" d="M1517.326,80.118L1635.117,89.432C1752.909,98.746,1988.492,117.373,2106.284,130.853C2224.076,144.333,2224.076,152.667,2224.076,168.949C2224.076,185.231,2224.076,209.462,2224.076,233.693C2224.076,257.924,2224.076,282.155,2224.076,298.438C2224.076,314.72,2224.076,323.053,2224.076,331.386C2224.076,339.72,2224.076,348.053,2224.076,362.886C2224.076,377.72,2224.076,399.053,2224.076,420.386C2224.076,441.72,2224.076,463.053,2224.076,477.886C2224.076,492.72,2224.076,501.053,2224.076,509.386C2224.076,517.72,2224.076,526.053,2224.076,534.386C2224.076,542.72,2224.076,551.053,2224.076,558.72C2224.076,566.386,2224.076,573.386,2224.076,576.886L2224.076,580.386"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ROUTER_EDGE_AI_15" d="M340.018,260.693L352.066,268.309C364.114,275.924,388.209,291.155,400.257,302.938C412.305,314.72,412.305,323.053,412.305,331.386C412.305,339.72,412.305,348.053,453.932,360.474C495.559,372.895,578.814,389.404,620.441,397.659L662.069,405.914"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ROUTER_EDGE_CACHE_16" d="M347.447,260.693L361.59,268.309C375.733,275.924,404.019,291.155,418.162,302.938C432.305,314.72,432.305,323.053,432.305,331.386C432.305,339.72,432.305,348.053,501.946,361.164C571.586,374.275,710.868,392.164,780.509,401.109L850.15,410.053"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EDGE_AI_ZHIPU_17" d="M723.445,459.386L722.204,463.553C720.964,467.72,718.483,476.053,717.243,484.386C716.003,492.72,716.003,501.053,716.003,509.386C716.003,517.72,716.003,526.053,774.648,534.386C833.294,542.72,950.586,551.053,1012.007,558.856C1073.429,566.66,1078.979,573.933,1081.755,577.57L1084.53,581.207"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EDGE_AI_SILICON_18" d="M788.542,459.386L794.256,463.553C799.97,467.72,811.399,476.053,817.114,484.386C822.828,492.72,822.828,501.053,822.828,509.386C822.828,517.72,822.828,526.053,879.965,534.386C937.102,542.72,1051.377,551.053,1116.93,559.757C1182.483,568.46,1199.316,577.534,1207.732,582.071L1216.148,586.608"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EDGE_CACHE_MONGODB_19" d="M930.602,459.386L930.602,463.553C930.602,467.72,930.602,476.053,930.602,484.386C930.602,492.72,930.602,501.053,930.602,509.386C930.602,517.72,930.602,526.053,924.948,534.386C919.294,542.72,907.987,551.053,879.527,561.782C851.066,572.511,805.452,585.635,782.646,592.198L759.839,598.76"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" marker-start="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LOCAL_DB_MONGODB_20" d="M610.992,285.386L610.992,288.886C610.992,292.386,610.992,299.386,610.992,307.053C610.992,314.72,610.992,323.053,610.992,331.386C610.992,339.72,610.992,348.053,610.992,362.886C610.992,377.72,610.992,399.053,610.992,420.386C610.992,441.72,610.992,463.053,610.992,477.886C610.992,492.72,610.992,501.053,610.992,509.386C610.992,517.72,610.992,526.053,610.992,534.386C610.992,542.72,610.992,551.053,614.642,558.912C618.292,566.771,625.591,574.156,629.241,577.849L632.89,581.541"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" marker-start="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LOCAL_CACHE_EDGE_CACHE_21" d="M930.602,283.715L930.602,287.494C930.602,291.272,930.602,298.829,930.602,306.775C930.602,314.72,930.602,323.053,930.602,331.386C930.602,339.72,930.602,348.053,930.602,355.72C930.602,363.386,930.602,370.386,930.602,373.886L930.602,377.386"></path><path marker-end="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointEnd)" marker-start="url(#mermaid-8f76b448-2268-4347-8033-502a847e12d6_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NAS_S3_22" d="M130.87,463.386L130.87,466.886C130.87,470.386,130.87,477.386,130.87,485.053C130.87,492.72,130.87,501.053,130.87,509.386C130.87,517.72,130.87,526.053,130.87,534.386C130.87,542.72,130.87,551.053,135.024,558.942C139.178,566.83,147.486,574.274,151.641,577.995L155.795,581.717"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1414.6484260559082, 72)" id="flowchart-CLIENT-914" class="node default clientClass"><rect height="78" width="205.3541717529297" y="-39" x="-102.67708587646484" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-72.67708587646484, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="145.3541717529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>NovelCraft 桌面应用<br>Electron App</p></span></div></foreignObject></g></g><g transform="translate(610.9921836853027, 233.69318389892578)" id="flowchart-LOCAL_DB-915" class="node default clientClass"><path transform="translate(-47.5, -47.69318181818182)" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:3px !important" class="basic label-container" d="M0,10.795454545454545 a47.5,10.795454545454545 0,0,0 95,0 a47.5,10.795454545454545 0,0,0 -95,0 l0,73.79545454545455 a47.5,10.795454545454545 0,0,0 95,0 l0,-73.79545454545455"></path><g transform="translate(-40, -14)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>本地数据库<br>SQLite</p></span></div></foreignObject></g></g><g transform="translate(930.6015586853027, 233.69318389892578)" id="flowchart-LOCAL_CACHE-916" class="node default clientClass"><path transform="translate(-39.5, -46.02205882352941)" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:3px !important" class="basic label-container" d="M0,9.681372549019608 a39.5,9.681372549019608 0,0,0 79,0 a39.5,9.681372549019608 0,0,0 -79,0 l0,72.68137254901961 a39.5,9.681372549019608 0,0,0 79,0 l0,-72.68137254901961"></path><g transform="translate(-32, -14)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>本地缓存<br>Redis</p></span></div></foreignObject></g></g><g transform="translate(1174.8281230926514, 233.69318389892578)" id="flowchart-LOCAL_AI-917" class="node default clientClass"><rect height="78" width="137.89583587646484" y="-39" x="-68.94791793823242" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-38.94791793823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="77.89583587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>本地AI服务<br>Ollama</p></span></div></foreignObject></g></g><g transform="translate(297.30468368530273, 233.69318389892578)" id="flowchart-ROUTER-918" class="node default localClass"><rect height="54" width="164.39583587646484" y="-27" x="-82.19791793823242" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-52.19791793823242, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="104.39583587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>路由器/防火墙</p></span></div></foreignObject></g></g><g transform="translate(130.86978912353516, 420.38636779785156)" id="flowchart-NAS-919" class="node default localClass"><rect height="78" width="175.73958587646484" y="-39" x="-87.86979293823242" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-57.86979293823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="115.73958587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>网络存储<br>NAS/文件服务器</p></span></div></foreignObject></g></g><g transform="translate(1116.7213459014893, 623.3863677978516)" id="flowchart-ZHIPU-920" class="node default aiClass"><rect height="78" width="105.89583587646484" y="-39" x="-52.94791793823242" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-22.947917938232422, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="45.895835876464844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>智谱AI<br>GLM-4</p></span></div></foreignObject></g></g><g transform="translate(1284.3723888397217, 623.3863677978516)" id="flowchart-SILICON-921" class="node default aiClass"><rect height="78" width="129.40625" y="-39" x="-64.703125" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-34.703125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="69.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>硅基流动<br>DeepSeek</p></span></div></foreignObject></g></g><g transform="translate(1471.778642654419, 623.3863677978516)" id="flowchart-GOOGLE-922" class="node default aiClass"><rect height="78" width="127.30208587646484" y="-39" x="-63.65104293823242" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-33.65104293823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="67.30208587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Google AI<br>Gemini</p></span></div></foreignObject></g></g><g transform="translate(1639.6901035308838, 623.3863677978516)" id="flowchart-GROK-923" class="node default aiClass"><rect height="78" width="108.52083587646484" y="-39" x="-54.26041793823242" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-24.260417938232422, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="48.520835876464844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GROK3<br>xAI</p></span></div></foreignObject></g></g><g transform="translate(674.2499904632568, 623.3863677978516)" id="flowchart-MONGODB-924" class="node default cloudClass"><rect height="78" width="163.48958587646484" y="-39" x="-81.74479293823242" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-51.74479293823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="103.48958587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MongoDB Atlas<br>云端数据库</p></span></div></foreignObject></g></g><g transform="translate(202.30468368530273, 623.3863677978516)" id="flowchart-S3-925" class="node default cloudClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>对象存储<br>S3/OSS</p></span></div></foreignObject></g></g><g transform="translate(392.30468368530273, 623.3863677978516)" id="flowchart-CDN-926" class="node default cloudClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>内容分发网络<br>CDN</p></span></div></foreignObject></g></g><g transform="translate(1849.7161464691162, 623.3863677978516)" id="flowchart-AUTH-927" class="node default cloudClass"><rect height="78" width="131.53125" y="-39" x="-65.765625" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-35.765625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="71.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>身份认证<br>OAuth 2.0</p></span></div></foreignObject></g></g><g transform="translate(2037.4921855926514, 623.3863677978516)" id="flowchart-MONITOR-928" class="node default cloudClass"><rect height="78" width="144.02083587646484" y="-39" x="-72.01041793823242" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-42.01041793823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="84.02083587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>监控服务<br>Prometheus</p></span></div></foreignObject></g></g><g transform="translate(2224.0755138397217, 623.3863677978516)" id="flowchart-LOG-929" class="node default cloudClass"><rect height="78" width="129.14583587646484" y="-39" x="-64.57291793823242" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-34.57291793823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="69.14583587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>日志服务<br>ELK Stack</p></span></div></foreignObject></g></g><g transform="translate(735.0546836853027, 420.38636779785156)" id="flowchart-EDGE_AI-930" class="node default edgeClass"><rect height="78" width="138.125" y="-39" x="-69.0625" style="fill:#ffebee !important;stroke:#c62828 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-39.0625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="78.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>边缘AI节点<br>GPU服务器</p></span></div></foreignObject></g></g><g transform="translate(930.6015586853027, 420.38636779785156)" id="flowchart-EDGE_CACHE-931" class="node default edgeClass"><rect height="78" width="152.96875" y="-39" x="-76.484375" style="fill:#ffebee !important;stroke:#c62828 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-46.484375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="92.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>边缘缓存<br>Redis Cluster</p></span></div></foreignObject></g></g></g></g></g></svg>