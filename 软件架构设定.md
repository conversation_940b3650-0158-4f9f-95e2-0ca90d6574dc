# 小说管理系统 - NovelCraft 软件架构设计

## 1. 系统概述

NovelCraft是一个全方位的小说创作与管理系统，旨在帮助作者从多个维度管理小说设定、剧情发展、人物关系等内容，并通过AI辅助生成、分析和续写功能，确保故事的连贯性和完整性。

## 2. 核心模块

### 2.1 数据管理模块
- **小说项目管理**：创建、编辑、删除、导入导出小说项目
- **版本控制系统**：追踪小说各版本的变更历史
- **数据存储与同步**：本地存储与云端备份同步
- **AI生成及补全、续写**AI通过访问数据库中已有的设定和剧情，生成新的设定、内容或续写现有内容。
### 2.2 设定管理模块
- **世界观设定**：地理、历史、文化、自然法则等
- **修炼体系**：能力体系、等级划分、修炼方法
- **政治系统**：国家、势力、组织结构、权力分配
- **经济系统**：货币、资源、交易方式
- **种族管理**：各类种族特性、分布、关系
- **关系网络**：势力、家族、组织间的关系图谱

### 2.3 人物管理模块
- **人物档案**：基础信息、性格特点、能力值、背景故事
- **人物关系网**：人物间的关系可视化
- **人物成长轨迹**：随剧情发展的人物变化追踪
- **人物画像生成**：基于描述的AI人物形象生成

### 2.4 剧情管理模块
- **主线剧情**：核心故事线索管理
- **支线剧情**：次要故事线索管理
- **章节管理**：章节内容、大纲、摘要
- **时间线管理**：事件发生的时间顺序可视化
- **剧情冲突检测**：自动检测剧情矛盾和逻辑问题

### 2.5 AI辅助模块
- **设定生成器**：自动生成世界观、修炼体系等设定
- **人物生成器**：创建符合设定的NPC和主要角色
- **剧情生成器**：基于已有设定生成新剧情
- **章节摘要器**：自动总结章节内容要点
- **续写引擎**：基于历史设定和剧情自动续写
- **设定一致性检查**：检查新内容是否符合已有设定

### 2.6 用户界面模块
- **项目仪表盘**：项目概览和快速访问
- **设定编辑器**：各类设定的编辑界面
- **剧情编辑器**：支持富文本和Markdown的编辑器
- **关系图谱可视化**：人物、势力关系的可视化展示
- **时间线可视化**：剧情发展时间线的可视化展示

## 3. 技术架构

### 3.1 前端技术
- **框架**：Electron (跨平台桌面应用)
- **UI库**：React + Ant Design
- **可视化**：D3.js (关系图谱、时间线可视化)
- **编辑器**：Monaco Editor (代码风格编辑) + Slate.js (富文本编辑)

### 3.2 后端技术
- **语言**：Python 3.9+
- **AI模型**：基于大型语言模型(LLM)的自定义微调模型
- **数据库**：SQLite (本地存储) + MongoDB (云端存储)
- **API服务**：FastAPI

### 3.3 AI引擎
- **文本生成**：基于GPT或类似模型的文本生成引擎
- **内容分析**：NLP技术进行文本分析、摘要和关键信息提取
- **一致性检查**：基于规则和语义理解的内容一致性验证

## 4. 数据流程

### 4.1 创作流程
1. 创建项目 → 设定基础世界观 → 构建人物关系 → 规划主线剧情 → 编写章节内容
2. AI辅助生成设定 → 人工审核修改 → 保存确认
3. 编写剧情 → AI检查一致性 → 修正问题 → 完成章节

### 4.2 续写流程
1. 导入历史设定和剧情 → AI分析关键元素 → 生成续写建议
2. 选择续写方向 → AI生成初稿 → 人工修改 → 整合到项目

### 4.3 数据同步流程
1. 本地编辑 → 自动保存 → 定时云端同步
2. 多设备访问 → 云端数据拉取 → 本地缓存

## 5. 系统扩展性

### 5.1 插件系统
- 支持第三方插件开发和集成
- 提供API接口供外部工具调用

### 5.2 模板系统
- 预设各类型小说的世界观模板
- 常见修炼体系、政治体系模板

### 5.3 导出功能
- 支持多种格式导出(Word, PDF, ePub等)
- 支持直接发布到各大小说平台

## 6. 安全与隐私

### 6.1 数据安全
- 本地数据加密存储
- 云端数据传输加密

### 6.2 隐私保护
- 用户可选择数据存储位置
- 明确的隐私政策和数据使用说明

## 7. 未来发展路线

### 7.1 短期目标
- 完成核心功能开发
- 优化AI生成质量
- 提升用户体验

### 7.2 中期目标
- 增加多语言支持
- 开发移动端应用
- 构建创作者社区

### 7.3 长期目标
- 开发专业版本针对出版社和专业作家
- 提供AI辅助的插画和配图功能
- 支持多媒体内容整合(音频、视频)

## 8. 详细模块设计

### 8.1 设定管理模块详细设计

#### 8.1.1 世界观设定系统
- **地理系统**
  - 世界地图编辑器：支持绘制、导入地图
  - 区域属性管理：气候、资源、人口等
  - 地点详情：城市、遗迹、特殊地点的详细信息
  
- **历史时间线**
  - 重大历史事件管理
  - 朝代/时代划分
  - 历史人物关联
  
- **文化系统**
  - 语言设定：主要语言、方言、特殊术语
  - 风俗习惯：节日、礼仪、禁忌
  - 宗教信仰：神祇体系、教义、仪式

#### 8.1.2 修炼体系设计器
- **能力体系构建**
  - 能力分类与等级划分
  - 能力获取与提升路径
  - 能力相克关系设定
  
- **修炼方法库**
  - 功法/技能体系
  - 修炼资源与材料
  - 境界划分与突破条件
  
- **特殊规则设定**
  - 天赋/血脉系统
  - 特殊状态与异常
  - 禁忌与限制

#### 8.1.3 政治经济系统
- **政体设计**
  - 国家/势力结构
  - 权力分配与制衡
  - 法律与规则体系
  
- **经济模型**
  - 货币系统设计
  - 资源分布与价值
  - 贸易关系与经济活动
  
- **势力关系网络**
  - 势力图谱可视化
  - 联盟与敌对关系
  - 势力影响力评估

### 8.2 AI辅助模块详细设计

#### 8.2.1 设定生成引擎
- **基于模板生成**
  - 预设模板库(奇幻、武侠、科幻等)
  - 模板参数化调整
  - 随机变异与创新点生成
  
- **基于关键词生成**
  - 用户输入关键词/概念
  - AI扩展与丰富设定
  - 多方案比较与选择
  
- **设定一致性验证**
  - 内部逻辑检查
  - 与已有设定的兼容性检查
  - 潜在问题预警

#### 8.2.2 剧情生成与分析系统
- **剧情结构生成**
  - 基于经典故事结构(三幕剧、英雄之旅等)
  - 冲突设计与高潮安排
  - 伏笔与悬念规划
  
- **章节内容分析**
  - 关键情节提取
  - 人物情感与动机分析
  - 设定元素使用统计
  
- **剧情续写引擎**
  - 基于历史剧情的延续性分析
  - 多线索整合与推进
  - 角色行为合理性预测

#### 8.2.3 人物系统
- **人物生成器**
  - 基于角色原型生成
  - 性格特质平衡与冲突设计
  - 成长轨迹规划
  
- **人物关系动态模拟**
  - 基于性格的互动预测
  - 关系演变模拟
  - 潜在冲突与合作点分析
  
- **人物画像生成**
  - 基于文字描述的视觉形象生成
  - 表情与姿态库
  - 风格化调整

### 8.3 用户界面模块详细设计

#### 8.3.1 多视图工作区
- **仪表盘视图**
  - 项目概览与进度
  - 最近编辑内容
  - 待办任务与提醒
  
- **设定百科视图**
  - 分类浏览设定内容
  - 搜索与过滤功能
  - 关联内容推荐
  
- **写作工作区**
  - 大纲与章节管理
  - 富文本编辑器
  - 实时设定参考面板

#### 8.3.2 可视化工具
- **关系图谱编辑器**
  - 交互式节点与连接创建
  - 关系类型与强度设定
  - 时间维度变化展示
  
- **时间线编辑器**
  - 事件创建与排序
  - 并行时间线管理
  - 关键节点高亮与注释
  
- **地图交互系统**
  - 多层次地图浏览
  - 地点标记与详情
  - 角色/势力活动区域可视化

## 9. 数据模型设计

### 9.1 核心数据实体

#### 9.1.1 项目(Project)
- 基本信息(id, 名称, 创建时间, 最后修改时间)
- 元数据(类型, 标签, 简介)
- 全局设置(使用的模板, AI设置)

#### 9.1.2 世界设定(WorldSetting)
- 基本信息(id, 项目id, 名称)
- 地理数据(地图, 区域, 地点)
- 历史数据(时间线, 事件)
- 规则数据(自然法则, 特殊规则)

#### 9.1.3 修炼体系(CultivationSystem)
- 基本信息(id, 项目id, 名称)
- 能力分类与等级
- 修炼方法与资源
- 特殊规则与限制

#### 9.1.4 人物(Character)
- 基本信息(id, 项目id, 名称, 类型)
- 属性数据(性格, 能力, 背景)
- 关系数据(与其他人物的关系)
- 成长数据(状态变化, 重要事件)

#### 9.1.5 势力(Faction)
- 基本信息(id, 项目id, 名称, 类型)
- 结构数据(组织结构, 成员)
- 资源数据(领地, 财富, 特殊资源)
- 关系数据(与其他势力的关系)

#### 9.1.6 剧情(Plot)
- 基本信息(id, 项目id, 名称, 类型)
- 结构数据(主线, 支线, 章节)
- 事件数据(关键事件, 转折点)
- 参与者数据(相关人物, 势力)

### 9.2 关系模型

#### 9.2.1 人物关系(CharacterRelation)
- 基本信息(id, 项目id)
- 关系双方(人物A, 人物B)
- 关系属性(类型, 强度, 开始时间, 结束时间)
- 关系事件(形成原因, 重要互动)

#### 9.2.2 势力关系(FactionRelation)
- 基本信息(id, 项目id)
- 关系双方(势力A, 势力B)
- 关系属性(类型, 强度, 开始时间, 结束时间)
- 关系事件(形成原因, 重要互动)

#### 9.2.3 事件关联(EventAssociation)
- 基本信息(id, 项目id)
- 事件数据(事件id, 类型)
- 关联实体(人物id, 势力id, 地点id)
- 关联属性(角色, 影响)

## 10. 系统交互流程

### 10.1 创作流程详细步骤

1. **项目初始化**
   - 创建新项目 → 选择类型模板 → 设置基本信息
   - AI生成初始世界设定建议 → 用户选择与修改
   - 确认基础设定 → 系统生成项目骨架

2. **世界构建**
   - 细化地理环境 → 设计政治体系 → 构建经济系统
   - 设计修炼/能力体系 → 定义规则与限制
   - AI检查设定一致性 → 修正冲突点

3. **人物创建**
   - 设计主要角色 → 定义性格与能力 → 设置成长轨迹
   - 创建次要角色 → 建立人物关系网
   - AI生成人物画像 → 完善人物细节

4. **剧情规划**
   - 设计主线剧情 → 规划关键转折点
   - 创建支线剧情 → 与主线整合
   - 设计章节结构 → 安排伏笔与悬念

5. **内容创作**
   - 编写章节内容 → 实时参考设定资料
   - AI辅助内容生成 → 用户修改调整
   - 章节完成 → AI自动总结与归档

### 10.2 AI辅助流程

1. **设定生成**
   - 用户输入关键概念 → AI生成多个设定方案
   - 用户选择基础方案 → 调整参数与细节
   - AI完善设定细节 → 生成完整设定文档

2. **剧情分析与建议**
   - 系统分析已有内容 → 识别剧情发展模式
   - 检测潜在逻辑问题 → 提供修正建议
   - 预测可能的发展方向 → 提供剧情建议

3. **续写辅助**
   - 分析历史剧情与设定 → 提取关键元素
   - 用户设定续写方向 → AI生成大纲建议
   - 用户确认大纲 → AI辅助内容生成

## 11. 技术实现细节

### 11.1 前端实现

#### 11.1.1 应用框架
- **Electron**：跨平台桌面应用框架
  - 主进程：负责文件系统访问、数据库操作
  - 渲染进程：负责UI渲染和用户交互
  - IPC通信：进程间数据传输

#### 11.1.2 UI组件
- **React**：用户界面库
  - 函数式组件 + Hooks架构
  - Context API用于状态管理
  - 自定义Hook封装业务逻辑
- **Ant Design**：UI组件库
  - 定制主题与样式
  - 响应式布局
  - 暗黑模式支持

#### 11.1.3 编辑器实现
- **富文本编辑器**
  - Slate.js作为核心框架
  - 自定义插件支持特殊内容(设定引用、角色标记)
  - 实时保存与历史记录
- **可视化编辑器**
  - D3.js实现关系图谱
  - Timeline.js实现时间线
  - Leaflet.js实现地图交互

### 11.2 后端实现

#### 11.2.1 本地服务
- **数据存储**
  - SQLite数据库：结构化数据存储
  - 文件系统：大型文本内容和资源文件
  - IndexedDB：客户端缓存
- **本地API服务**
  - Express.js提供RESTful API
  - WebSocket支持实时通信
  - 本地缓存机制

#### 11.2.2 云服务
- **数据同步**
  - MongoDB Atlas：云端数据存储
  - Firebase Realtime Database：实时数据同步
  - S3兼容存储：资源文件存储
- **用户认证**
  - OAuth 2.0认证
  - JWT令牌管理
  - 权限控制系统

#### 11.2.3 AI服务
- **模型部署**
  - 云端API：高计算量任务
  - 本地轻量级模型：基础任务
  - 混合推理策略
- **API集成**
  - OpenAI API：文本生成与分析
  - Stable Diffusion API：图像生成
  - 自定义微调模型API

### 11.3 数据库设计

#### 11.3.1 关系型数据库(SQLite)
- **表结构**
  - 项目表(projects)
  - 设定表(settings)
  - 人物表(characters)
  - 势力表(factions)
  - 关系表(relations)
  - 事件表(events)
  - 章节表(chapters)
- **索引优化**
  - 复合索引：提高查询效率
  - 全文索引：支持内容搜索
  - 外键约束：保证数据完整性

#### 11.3.2 文档型数据库(MongoDB)
- **集合设计**
  - 项目集合(projects)

额外补充参考

一、AI助手功能模块设计（高扩展性）
1. 编剧AI（Director AI）
功能：根据用户给定的方向和命题，生成详细的大纲、世界观、人物设定、剧情主线等。
输入：命题、方向、主题关键词
输出：大纲、设定、主线剧情、章节规划
2. 剧情写作AI（Writer AI，RWKV为主）
功能：结合主题、大纲、时间线、设定等，分章节生成详细剧情文本。
输入：大纲、章节规划、设定、时间线
输出：章节详细内容
3. 总结AI（Summarizer AI）
功能：对每章节、每卷（部）进行总结，生成下一卷宗的前言。
输入：章节内容、卷内容
输出：章节总结、卷总结、前言
4. 读者AI（Reader AI）
功能：模拟读者对内容进行正向/负向评价，提出建议，辅助编剧AI和写作AI调整剧情和文风。
输入：章节内容、卷内容、总结
输出：评价、建议、反馈
5. AI协作与反馈机制
功能：各AI模块之间可自动传递内容，形成闭环反馈，支持人工干预和二次编辑。
输入/输出：各AI模块的输入输出

二、系统架构扩展建议
AI服务抽象层：所有AI助手通过统一接口调用，便于后续扩展新AI类型。
任务队列与流程引擎：支持多AI协作的任务流转与状态管理。
可视化流程管理：前端可视化展示AI协作流程、内容流转和反馈。

三、README.md结构补充建议
新增：AI编剧协作系统说明
## 致谢

感谢所有为NovelCraft项目做出贡献的开发者和用户。

## Agent-AI编剧协作系统

### 功能概述

本系统集成多种AI助手，协同完成小说从命题到完本的全过程创作。各AI助手分工明确，互相协作，支持人工干预和反馈优化。

### 主要Agent-AI助手模块

1. **编剧AI（Director AI）**
   - 负责根据命题生成大纲、设定、主线剧情。
   - 输入：命题、方向、主题
   - 输出：大纲、设定、主线剧情

2. **剧情写作AI（Writer AI，RWKV）**
   - 负责分章节生成详细剧情。
   - 输入：大纲、章节规划、设定、时间线
   - 输出：章节内容

3. **总结AI（Summarizer AI）**
   - 负责已完成的章节、卷的总结，生成后续章节、卷宗的前言。
   - 输入：章节/卷内容
   - 输出：总结、前言

4. **读者AI（Reader AI）**
   - 负责模拟读者评价，提出正负反馈和建议。
   - 输入：章节/卷内容、总结
   - 输出：评价、建议

5. **AI协作与反馈机制**
   - 各AI模块自动流转内容，支持人工干预和二次编辑。

### 工作流程

1. 用户输入命题/方向
2. 编剧AI生成大纲、设定、主线
3. 剧情写作AI分章节生成内容
4. 总结AI对章节/卷进行总结，生成前言
5. 读者AI给出评价和建议
6. 编剧AI/写作AI根据反馈调整内容
7. 循环迭代，直至小说完本

### 扩展性说明

- 所有AI助手通过统一接口调用，便于扩展新AI类型
- 支持多AI协作、任务流转、人工干预
- 可视化流程管理，便于内容追踪和优化

### 主要调用方式

- 通过系统设置界面配置各AI助手参数
- 在创作流程中选择AI助手自动/手动生成内容
- 支持一键生成、逐步生成、人工编辑与反馈

### Agent-代码结构建议
ai_engine/
├── director/ # 编剧AI相关逻辑
│ └── director_ai.py
├── writer/ # 剧情写作AI（RWKV等）
│ └── writer_ai.py
├── summarizer/ # 总结AI
│ └── summarizer_ai.py
├── reader/ # 读者AI
│ └── reader_ai.py
├── workflow/ # AI协作与流程管理
│ └── workflow_engine.py
└── utils/ # 工具函数


- 每个AI助手为独立子模块，便于维护和扩展
- 统一接口，支持多模型、多平台切换
- 支持任务队列和流程引擎，便于多AI协作

### 备注

- 推荐优先支持ollama、RWKV等本地模型，保障数据安全和隐私
- 支持zhipuAI、deepseek、硅基流动等云端模型作为补充
- 后续可扩展更多AI助手类型和协作模式
-