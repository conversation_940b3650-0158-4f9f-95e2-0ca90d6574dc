<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2120.21875 1478.803955078125" style="max-width: 2120.21875px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36"><style>#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .error-icon{fill:#a44141;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .edge-thickness-normal{stroke-width:1px;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .marker.cross{stroke:lightgrey;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 p{margin:0;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .cluster-label text{fill:#F9FFFE;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .cluster-label span{color:#F9FFFE;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .cluster-label span p{background-color:transparent;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .label text,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 span{fill:#ccc;color:#ccc;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .node rect,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .node circle,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .node ellipse,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .node polygon,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .rough-node .label text,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .node .label text,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .image-shape .label,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .icon-shape .label{text-anchor:middle;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .rough-node .label,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .node .label,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .image-shape .label,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .icon-shape .label{text-align:center;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .node.clickable{cursor:pointer;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .arrowheadPath{fill:lightgrey;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .cluster text{fill:#F9FFFE;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .cluster span{color:#F9FFFE;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 rect.text{fill:none;stroke-width:0;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .icon-shape,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .icon-shape p,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .icon-shape rect,#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .userClass&gt;*{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:3px!important;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .userClass span{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:3px!important;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .aiClass&gt;*{fill:#e3f2fd!important;stroke:#1565c0!important;stroke-width:2px!important;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .aiClass span{fill:#e3f2fd!important;stroke:#1565c0!important;stroke-width:2px!important;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .dataClass&gt;*{fill:#fff8e1!important;stroke:#f57f17!important;stroke-width:2px!important;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .dataClass span{fill:#fff8e1!important;stroke:#f57f17!important;stroke-width:2px!important;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .extClass&gt;*{fill:#fce4ec!important;stroke:#ad1457!important;stroke-width:2px!important;}#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36 .extClass span{fill:#fce4ec!important;stroke:#ad1457!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="外部AI服务" class="cluster"><rect height="757" width="193" y="713.8039646148682" x="1919.218765258789" style=""></rect><g transform="translate(1976.7708473205566, 713.8039646148682)" class="cluster-label"><foreignObject height="24" width="77.89583587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>外部AI服务</p></span></div></foreignObject></g></g><g data-look="classic" id="数据管理层" class="cluster"><rect height="576.5033683776855" width="193" y="8" x="1919.218765258789" style=""></rect><g transform="translate(1975.718765258789, 8)" class="cluster-label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据管理层</p></span></div></foreignObject></g></g><g data-look="classic" id="核心AI助手群" class="cluster"><rect height="1151.4051570892334" width="1448.593765258789" y="21.398807525634766" x="420.625" style=""></rect><g transform="translate(1097.973964691162, 21.398807525634766)" class="cluster-label"><foreignObject height="24" width="93.89583587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>核心AI助手群</p></span></div></foreignObject></g></g><g data-look="classic" id="用户交互层" class="cluster"><rect height="922.6534729003906" width="362.625" y="317.15049171447754" x="8" style=""></rect><g transform="translate(149.3125, 317.15049171447754)" class="cluster-label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户交互层</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" marker-start="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_User_ConvAI_0" d="M129,460.804L132.5,460.804C136,460.804,143,460.804,150,460.804C157,460.804,164,460.804,167.5,460.804L171,460.804"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ConvAI_DirectorAI_1" d="M345.625,472.637L349.792,473.215C353.958,473.793,362.292,474.949,370.625,475.527C378.958,476.105,387.292,476.105,395.625,476.105C403.958,476.105,412.292,476.105,419.958,476.105C427.625,476.105,434.625,476.105,438.125,476.105L441.625,476.105"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ConvAI_WriterAI_2" d="M287.319,499.804L301.204,519.854C315.088,539.904,342.856,580.004,360.907,600.054C378.958,620.105,387.292,620.105,395.625,620.105C403.958,620.105,412.292,620.105,435.858,620.105C459.424,620.105,498.222,620.105,543.688,620.105C589.153,620.105,641.285,620.105,694.75,620.105C748.215,620.105,803.014,620.105,856.479,620.105C909.944,620.105,962.076,620.105,997.049,615.543C1032.022,610.982,1049.835,601.859,1058.741,597.297L1067.648,592.736"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ConvAI_SettingAI_3" d="M309.961,421.804L320.071,413.862C330.182,405.919,350.404,390.035,364.681,382.093C378.958,374.15,387.292,374.15,395.625,374.15C403.958,374.15,412.292,374.15,435.858,374.15C459.424,374.15,498.222,374.15,543.688,374.15C589.153,374.15,641.285,374.15,677.545,371.112C713.806,368.073,734.194,361.996,744.389,358.958L754.583,355.919"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DirectorAI_SettingAI_4" d="M589.859,425.105L607.119,408.446C624.378,391.787,658.898,358.469,686.324,341.81C713.75,325.15,734.083,325.15,744.25,325.15L754.417,325.15"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DirectorAI_WriterAI_5" d="M628.417,515.843L639.25,520.553C650.083,525.263,671.75,534.684,709.983,539.394C748.215,544.105,803.014,544.105,856.479,544.105C909.944,544.105,962.076,544.105,996.976,544.105C1031.875,544.105,1049.542,544.105,1058.375,544.105L1067.208,544.105"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WriterAI_SummarizerAI_6" d="M1185.699,595.105L1206.583,641.221C1227.466,687.338,1269.233,779.571,1298.961,824.06C1328.689,868.549,1346.377,865.293,1355.222,863.666L1364.066,862.038"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SummarizerAI_DirectorAI_7" d="M1368,820.956L1358.5,818.431C1349,815.905,1330,810.855,1295.767,808.329C1261.535,805.804,1212.069,805.804,1162.604,805.804C1113.139,805.804,1063.674,805.804,1012.875,805.804C962.076,805.804,909.944,805.804,856.479,805.804C803.014,805.804,748.215,805.804,699.068,759.956C649.92,714.109,606.424,622.414,584.676,576.566L562.927,530.719"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SummarizerAI_ReaderAI_8" d="M1547.427,844.804L1556.927,844.804C1566.427,844.804,1585.427,844.804,1606.232,837.338C1627.038,829.872,1649.648,814.94,1660.953,807.474L1672.259,800.008"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReaderAI_WriterAI_9" d="M1661.427,746.804L1651.927,746.804C1642.427,746.804,1623.427,746.804,1589.475,746.804C1555.523,746.804,1506.618,746.804,1457.714,746.804C1408.809,746.804,1359.905,746.804,1317.336,722.059C1274.768,697.313,1238.536,647.823,1220.42,623.077L1202.304,598.332"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReaderAI_DirectorAI_10" d="M1661.427,720.321L1651.927,717.568C1642.427,714.815,1623.427,709.31,1589.475,706.557C1555.523,703.804,1506.618,703.804,1457.714,703.804C1408.809,703.804,1359.905,703.804,1310.72,703.804C1261.535,703.804,1212.069,703.804,1162.604,703.804C1113.139,703.804,1063.674,703.804,1012.875,703.804C962.076,703.804,909.944,703.804,856.479,703.804C803.014,703.804,748.215,703.804,700.966,674.904C653.716,646.003,614.016,588.202,594.165,559.302L574.315,530.402"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SettingAI_WriterAI_11" d="M912.821,376.15L929.719,391.817C946.617,407.484,980.412,438.817,1006.214,458.921C1032.015,479.025,1049.822,487.899,1058.725,492.336L1067.628,496.773"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SettingAI_DirectorAI_12" d="M758.417,295.524L747.583,292.295C736.75,289.066,715.083,282.608,685.243,303.68C655.403,324.752,617.389,373.353,598.382,397.653L579.375,421.954"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DirectorAI_WorldDB_13" d="M560.39,425.105L582.561,376.72C604.732,328.336,649.075,231.567,698.645,183.182C748.215,134.798,803.014,134.798,856.479,134.798C909.944,134.798,962.076,134.798,1012.875,134.798C1063.674,134.798,1113.139,134.798,1162.604,134.798C1212.069,134.798,1261.535,134.798,1310.72,134.798C1359.905,134.798,1408.809,134.798,1457.714,134.798C1506.618,134.798,1555.523,134.798,1604.707,134.798C1653.892,134.798,1703.358,134.798,1747.49,134.798C1791.622,134.798,1830.42,134.798,1853.986,134.798C1877.552,134.798,1885.885,134.798,1894.219,134.798C1902.552,134.798,1910.885,134.798,1920.065,130.11C1929.245,125.422,1939.271,116.046,1944.284,111.358L1949.297,106.67"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DirectorAI_CharDB_14" d="M570.682,425.105L591.138,394.112C611.594,363.12,652.505,301.135,700.36,270.143C748.215,239.15,803.014,239.15,856.479,239.15C909.944,239.15,962.076,239.15,1012.875,239.15C1063.674,239.15,1113.139,239.15,1162.604,239.15C1212.069,239.15,1261.535,239.15,1310.72,239.15C1359.905,239.15,1408.809,239.15,1457.714,239.15C1506.618,239.15,1555.523,239.15,1604.707,239.15C1653.892,239.15,1703.358,239.15,1747.49,239.15C1791.622,239.15,1830.42,239.15,1853.986,239.15C1877.552,239.15,1885.885,239.15,1894.219,239.15C1902.552,239.15,1910.885,239.15,1919.917,237.59C1928.949,236.029,1938.68,232.908,1943.545,231.348L1948.41,229.787"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DirectorAI_PlotDB_15" d="M628.417,447.496L639.25,444.105C650.083,440.714,671.75,433.932,709.983,430.541C748.215,427.15,803.014,427.15,856.479,427.15C909.944,427.15,962.076,427.15,1012.875,427.15C1063.674,427.15,1113.139,427.15,1162.604,427.15C1212.069,427.15,1261.535,427.15,1310.72,427.15C1359.905,427.15,1408.809,427.15,1457.714,427.15C1506.618,427.15,1555.523,427.15,1604.707,427.15C1653.892,427.15,1703.358,427.15,1747.49,427.15C1791.622,427.15,1830.42,427.15,1853.986,427.15C1877.552,427.15,1885.885,427.15,1894.219,427.15C1902.552,427.15,1910.885,427.15,1923.682,418.609C1936.479,410.067,1953.739,392.984,1962.37,384.442L1971,375.9"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WriterAI_ChapterDB_16" d="M1254,544.105L1263.5,544.105C1273,544.105,1292,544.105,1325.952,544.105C1359.905,544.105,1408.809,544.105,1457.714,544.105C1506.618,544.105,1555.523,544.105,1604.707,544.105C1653.892,544.105,1703.358,544.105,1747.49,544.105C1791.622,544.105,1830.42,544.105,1853.986,544.105C1877.552,544.105,1885.885,544.105,1894.219,544.105C1902.552,544.105,1910.885,544.105,1919.921,542.439C1928.957,540.774,1938.696,537.444,1943.565,535.779L1948.434,534.114"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WriterAI_WorldDB_17" d="M1178.961,493.105L1200.967,424.487C1222.974,355.869,1266.987,218.634,1313.446,150.016C1359.905,81.399,1408.809,81.399,1457.714,81.399C1506.618,81.399,1555.523,81.399,1604.707,81.399C1653.892,81.399,1703.358,81.399,1747.49,81.399C1791.622,81.399,1830.42,81.399,1853.986,81.399C1877.552,81.399,1885.885,81.399,1894.219,81.399C1902.552,81.399,1910.885,81.399,1919.885,81.399C1928.885,81.399,1938.552,81.399,1943.385,81.399L1948.219,81.399"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WriterAI_CharDB_18" d="M1185.135,493.105L1206.112,445.62C1227.09,398.135,1269.045,303.166,1314.475,255.681C1359.905,208.196,1408.809,208.196,1457.714,208.196C1506.618,208.196,1555.523,208.196,1604.707,208.196C1653.892,208.196,1703.358,208.196,1747.49,208.196C1791.622,208.196,1830.42,208.196,1853.986,208.196C1877.552,208.196,1885.885,208.196,1894.219,208.196C1902.552,208.196,1910.885,208.196,1919.885,208.196C1928.885,208.196,1938.552,208.196,1943.385,208.196L1948.219,208.196"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SettingAI_WorldDB_19" d="M888.054,274.15L909.08,238.692C930.105,203.233,972.157,132.316,1017.915,96.857C1063.674,61.399,1113.139,61.399,1162.604,61.399C1212.069,61.399,1261.535,61.399,1310.72,61.399C1359.905,61.399,1408.809,61.399,1457.714,61.399C1506.618,61.399,1555.523,61.399,1604.707,61.399C1653.892,61.399,1703.358,61.399,1747.49,61.399C1791.622,61.399,1830.42,61.399,1853.986,61.399C1877.552,61.399,1885.885,61.399,1894.219,61.399C1902.552,61.399,1910.885,61.399,1919.899,62.403C1928.913,63.408,1938.608,65.417,1943.455,66.422L1948.302,67.426"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SettingAI_CharDB_20" d="M904.634,274.15L922.896,254.258C941.159,234.366,977.684,194.582,1020.679,174.69C1063.674,154.798,1113.139,154.798,1162.604,154.798C1212.069,154.798,1261.535,154.798,1310.72,154.798C1359.905,154.798,1408.809,154.798,1457.714,154.798C1506.618,154.798,1555.523,154.798,1604.707,154.798C1653.892,154.798,1703.358,154.798,1747.49,154.798C1791.622,154.798,1830.42,154.798,1853.986,154.798C1877.552,154.798,1885.885,154.798,1894.219,154.798C1902.552,154.798,1910.885,154.798,1920.065,159.486C1929.245,164.174,1939.271,173.55,1944.284,178.237L1949.297,182.925"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SettingAI_PlotDB_21" d="M957.208,325.15L966.708,325.15C976.208,325.15,995.208,325.15,1029.441,325.15C1063.674,325.15,1113.139,325.15,1162.604,325.15C1212.069,325.15,1261.535,325.15,1310.72,325.15C1359.905,325.15,1408.809,325.15,1457.714,325.15C1506.618,325.15,1555.523,325.15,1604.707,325.15C1653.892,325.15,1703.358,325.15,1747.49,325.15C1791.622,325.15,1830.42,325.15,1853.986,325.15C1877.552,325.15,1885.885,325.15,1894.219,325.15C1902.552,325.15,1910.885,325.15,1918.556,325.548C1926.227,325.946,1933.236,326.742,1936.74,327.139L1940.244,327.537"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SummarizerAI_ChapterDB_22" d="M1480.136,793.804L1500.851,746.687C1521.566,699.571,1562.997,605.338,1608.445,558.221C1653.892,511.105,1703.358,511.105,1747.49,511.105C1791.622,511.105,1830.42,511.105,1853.986,511.105C1877.552,511.105,1885.885,511.105,1894.219,511.105C1902.552,511.105,1910.885,511.105,1919.885,511.105C1928.885,511.105,1938.552,511.105,1943.385,511.105L1948.219,511.105"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReaderAI_ChapterDB_23" d="M1772.633,695.804L1788.731,654.362C1804.828,612.919,1837.024,530.035,1857.288,488.593C1877.552,447.15,1885.885,447.15,1894.219,447.15C1902.552,447.15,1910.885,447.15,1920.972,452.217C1931.059,457.283,1942.9,467.416,1948.82,472.482L1954.74,477.548"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DirectorAI_Ollama_24" d="M580.206,527.105L599.074,549.388C617.943,571.671,655.68,616.237,701.947,638.521C748.215,660.804,803.014,660.804,856.479,660.804C909.944,660.804,962.076,660.804,1012.875,660.804C1063.674,660.804,1113.139,660.804,1162.604,660.804C1212.069,660.804,1261.535,660.804,1310.72,660.804C1359.905,660.804,1408.809,660.804,1457.714,660.804C1506.618,660.804,1555.523,660.804,1604.707,660.804C1653.892,660.804,1703.358,660.804,1747.49,660.804C1791.622,660.804,1830.42,660.804,1853.986,683.637C1877.552,706.471,1885.885,752.137,1894.219,774.971C1902.552,797.804,1910.885,797.804,1920.139,797.277C1929.393,796.75,1939.566,795.695,1944.653,795.168L1949.74,794.641"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WriterAI_ZhipuAI_25" d="M1182.175,595.105L1203.646,651.054C1225.117,707.004,1268.058,818.904,1313.982,874.854C1359.905,930.804,1408.809,930.804,1457.714,930.804C1506.618,930.804,1555.523,930.804,1604.707,930.804C1653.892,930.804,1703.358,930.804,1747.49,930.804C1791.622,930.804,1830.42,930.804,1853.986,944.471C1877.552,958.137,1885.885,985.471,1894.219,999.137C1902.552,1012.804,1910.885,1012.804,1920.135,1012.804C1929.385,1012.804,1939.552,1012.804,1944.635,1012.804L1949.719,1012.804"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SettingAI_SiliconAI_26" d="M869.548,376.15L893.658,480.926C917.768,585.702,965.988,795.253,1014.831,900.028C1063.674,1004.804,1113.139,1004.804,1162.604,1004.804C1212.069,1004.804,1261.535,1004.804,1310.72,1004.804C1359.905,1004.804,1408.809,1004.804,1457.714,1004.804C1506.618,1004.804,1555.523,1004.804,1604.707,1004.804C1653.892,1004.804,1703.358,1004.804,1747.49,1004.804C1791.622,1004.804,1830.42,1004.804,1853.986,1027.471C1877.552,1050.137,1885.885,1095.471,1894.219,1118.137C1902.552,1140.804,1910.885,1140.804,1918.977,1140.804C1927.069,1140.804,1934.92,1140.804,1938.846,1140.804L1942.771,1140.804"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SummarizerAI_GoogleAI_27" d="M1489.69,895.804L1508.813,926.304C1527.935,956.804,1566.181,1017.804,1610.037,1048.304C1653.892,1078.804,1703.358,1078.804,1747.49,1078.804C1791.622,1078.804,1830.42,1078.804,1853.986,1110.471C1877.552,1142.137,1885.885,1205.471,1894.219,1237.137C1902.552,1268.804,1910.885,1268.804,1920.135,1268.804C1929.385,1268.804,1939.552,1268.804,1944.635,1268.804L1949.719,1268.804"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReaderAI_GROKAI_28" d="M1767.444,797.804L1784.407,856.971C1801.369,916.137,1835.294,1034.471,1856.423,1134.304C1877.552,1234.137,1885.885,1315.471,1894.219,1356.137C1902.552,1396.804,1910.885,1396.804,1919.94,1396.804C1928.995,1396.804,1938.771,1396.804,1943.659,1396.804L1948.547,1396.804"></path><path marker-end="url(#mermaid-6f1e696a-0333-43ca-aebc-a47e98bbfc36_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ConvAI_Ollama_29" d="M266.19,499.804L283.596,615.304C301.002,730.804,335.813,961.804,357.386,1077.304C378.958,1192.804,387.292,1192.804,395.625,1192.804C403.958,1192.804,412.292,1192.804,435.858,1192.804C459.424,1192.804,498.222,1192.804,543.688,1192.804C589.153,1192.804,641.285,1192.804,694.75,1192.804C748.215,1192.804,803.014,1192.804,856.479,1192.804C909.944,1192.804,962.076,1192.804,1012.875,1192.804C1063.674,1192.804,1113.139,1192.804,1162.604,1192.804C1212.069,1192.804,1261.535,1192.804,1310.72,1192.804C1359.905,1192.804,1408.809,1192.804,1457.714,1192.804C1506.618,1192.804,1555.523,1192.804,1604.707,1192.804C1653.892,1192.804,1703.358,1192.804,1747.49,1192.804C1791.622,1192.804,1830.42,1192.804,1853.986,1123.637C1877.552,1054.471,1885.885,916.137,1894.219,846.971C1902.552,777.804,1910.885,777.804,1920.139,778.331C1929.393,778.858,1939.566,779.913,1944.653,780.44L1949.74,780.967"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(693.4166717529297, 325.15049171447754)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>生成设定</p></span></div></foreignObject></g></g><g transform="translate(857.8125076293945, 544.1045608520508)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>提供大纲</p></span></div></foreignObject></g></g><g transform="translate(1311.000015258789, 871.8039646148682)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>内容输出</p></span></div></foreignObject></g></g><g transform="translate(1014.2083435058594, 805.8039646148682)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>总结反馈</p></span></div></foreignObject></g></g><g transform="translate(1604.4270935058594, 844.8039646148682)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>内容分析</p></span></div></foreignObject></g></g><g transform="translate(1457.7135543823242, 746.8039646148682)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>质量反馈</p></span></div></foreignObject></g></g><g transform="translate(1162.6041793823242, 703.8039646148682)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>改进建议</p></span></div></foreignObject></g></g><g transform="translate(1014.2083435058594, 470.15049171447754)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>设定验证</p></span></div></foreignObject></g></g><g transform="translate(693.4166717529297, 276.15049171447754)" class="edgeLabel"><g transform="translate(-40, -12)" class="label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>一致性检查</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(79, 460.80396461486816)" id="flowchart-User-315" class="node default userClass"><rect height="54" width="92" y="-27" x="-46" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-16, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户</p></span></div></foreignObject></g></g><g transform="translate(260.3125, 460.80396461486816)" id="flowchart-ConvAI-316" class="node default userClass"><rect height="78" width="170.625" y="-39" x="-85.3125" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-55.3125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="110.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>对话管理AI<br>Conversation AI</p></span></div></foreignObject></g></g><g transform="translate(537.0208358764648, 476.1045608520508)" id="flowchart-DirectorAI-317" class="node default aiClass"><rect height="102" width="182.7916717529297" y="-51" x="-91.39583587646484" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-61.395835876464844, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="122.79167175292969"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>编剧AI<br>Director AI<br>📝 大纲设定生成</p></span></div></foreignObject></g></g><g transform="translate(1162.6041793823242, 544.1045608520508)" id="flowchart-WriterAI-318" class="node default aiClass"><rect height="102" width="182.7916717529297" y="-51" x="-91.39583587646484" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-61.395835876464844, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="122.79167175292969"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>写作AI<br>Writer AI<br>✍️ 章节内容创作</p></span></div></foreignObject></g></g><g transform="translate(857.8125076293945, 325.15049171447754)" id="flowchart-SettingAI-319" class="node default aiClass"><rect height="102" width="198.7916717529297" y="-51" x="-99.39583587646484" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-69.39583587646484, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="138.7916717529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设定管理AI<br>Setting Manager AI<br>⚙️ 设定一致性管理</p></span></div></foreignObject></g></g><g transform="translate(1457.7135543823242, 844.8039646148682)" id="flowchart-SummarizerAI-320" class="node default aiClass"><rect height="102" width="179.42708587646484" y="-51" x="-89.71354293823242" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-59.71354293823242, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="119.42708587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>总结AI<br>Summarizer AI<br>📋 内容总结归纳</p></span></div></foreignObject></g></g><g transform="translate(1752.8229293823242, 746.8039646148682)" id="flowchart-ReaderAI-321" class="node default aiClass"><rect height="102" width="182.7916717529297" y="-51" x="-91.39583587646484" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-61.395835876464844, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="122.79167175292969"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读者AI<br>Reader AI<br>👥 质量评估反馈</p></span></div></foreignObject></g></g><g transform="translate(2015.718765258789, 81.39880752563477)" id="flowchart-WorldDB-322" class="node default dataClass"><path transform="translate(-63.5, -38.398809523809526)" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container" d="M0,12.59920634920635 a63.5,12.59920634920635 0,0,0 127,0 a63.5,12.59920634920635 0,0,0 -127,0 l0,51.59920634920635 a63.5,12.59920634920635 0,0,0 127,0 l0,-51.59920634920635"></path><g transform="translate(-56, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>世界设定数据库</p></span></div></foreignObject></g></g><g transform="translate(2015.718765258789, 208.1964225769043)" id="flowchart-CharDB-323" class="node default dataClass"><path transform="translate(-63.5, -38.398809523809526)" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container" d="M0,12.59920634920635 a63.5,12.59920634920635 0,0,0 127,0 a63.5,12.59920634920635 0,0,0 -127,0 l0,51.59920634920635 a63.5,12.59920634920635 0,0,0 127,0 l0,-51.59920634920635"></path><g transform="translate(-56, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>人物关系数据库</p></span></div></foreignObject></g></g><g transform="translate(2015.718765258789, 336.1045608520508)" id="flowchart-PlotDB-324" class="node default dataClass"><path transform="translate(-71.5, -39.50932835820896)" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container" d="M0,13.339552238805972 a71.5,13.339552238805972 0,0,0 143,0 a71.5,13.339552238805972 0,0,0 -143,0 l0,52.33955223880597 a71.5,13.339552238805972 0,0,0 143,0 l0,-52.33955223880597"></path><g transform="translate(-64, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>剧情时间线数据库</p></span></div></foreignObject></g></g><g transform="translate(2015.718765258789, 511.1045608520508)" id="flowchart-ChapterDB-325" class="node default dataClass"><path transform="translate(-63.5, -38.398809523809526)" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container" d="M0,12.59920634920635 a63.5,12.59920634920635 0,0,0 127,0 a63.5,12.59920634920635 0,0,0 -127,0 l0,51.59920634920635 a63.5,12.59920634920635 0,0,0 127,0 l0,-51.59920634920635"></path><g transform="translate(-56, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>章节内容数据库</p></span></div></foreignObject></g></g><g transform="translate(2015.718765258789, 787.8039646148682)" id="flowchart-Ollama-326" class="node default extClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#fce4ec !important;stroke:#ad1457 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Ollama<br>本地模型</p></span></div></foreignObject></g></g><g transform="translate(2015.718765258789, 1012.8039646148682)" id="flowchart-ZhipuAI-327" class="node default extClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#fce4ec !important;stroke:#ad1457 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>智谱AI<br>云端服务</p></span></div></foreignObject></g></g><g transform="translate(2015.718765258789, 1140.8039646148682)" id="flowchart-SiliconAI-328" class="node default extClass"><rect height="78" width="137.89583587646484" y="-39" x="-68.94791793823242" style="fill:#fce4ec !important;stroke:#ad1457 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-38.94791793823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="77.89583587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>硅基流动AI<br>云端服务</p></span></div></foreignObject></g></g><g transform="translate(2015.718765258789, 1268.8039646148682)" id="flowchart-GoogleAI-329" class="node default extClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#fce4ec !important;stroke:#ad1457 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>谷歌AI<br>云端服务</p></span></div></foreignObject></g></g><g transform="translate(2015.718765258789, 1396.8039646148682)" id="flowchart-GROKAI-330" class="node default extClass"><rect height="78" width="126.34375" y="-39" x="-63.171875" style="fill:#fce4ec !important;stroke:#ad1457 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-33.171875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="66.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GROK3 AI<br>云端服务</p></span></div></foreignObject></g></g></g></g></g></svg>