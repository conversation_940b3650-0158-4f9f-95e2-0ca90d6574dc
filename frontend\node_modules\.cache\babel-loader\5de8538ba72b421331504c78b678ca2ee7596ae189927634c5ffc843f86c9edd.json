{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\AIAssistant.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Card, Typography, Button, Input, Select, Row, Col, Tabs, Form, message, Spin, Space, Tag, Divider, Alert, Modal, List, Slider, Switch, InputNumber, Badge, Popconfirm, Drawer } from 'antd';\nimport { RobotOutlined, MessageOutlined, SendOutlined, BulbOutlined, UserOutlined, ReloadOutlined, BookOutlined, EditOutlined, CheckCircleOutlined, HistoryOutlined, DownloadOutlined, CopyOutlined, DeleteOutlined, SettingOutlined, FileTextOutlined, ThunderboltOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  TabPane\n} = Tabs;\nconst AIAssistant = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [providers, setProviders] = useState([]);\n  const [currentProvider, setCurrentProvider] = useState('');\n  const [aiStatus, setAiStatus] = useState({\n    connected: false,\n    status: 'offline'\n  });\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [generateForm] = Form.useForm();\n  const [plotForm] = Form.useForm();\n  const [continueForm] = Form.useForm();\n  const [checkForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('chat');\n  const messagesEndRef = useRef(null);\n\n  // 新增状态\n  const [generatedContent, setGeneratedContent] = useState('');\n  const [contentHistory, setContentHistory] = useState([]);\n  const [showHistory, setShowHistory] = useState(false);\n  const [showTemplates, setShowTemplates] = useState(false);\n  const [showSettings, setShowSettings] = useState(false);\n  const [templates, setTemplates] = useState([]);\n  const [aiParams, setAiParams] = useState({\n    temperature: 0.7,\n    max_tokens: 2000,\n    top_p: 1.0,\n    frequency_penalty: 0.0,\n    presence_penalty: 0.0\n  });\n  const [batchMode, setBatchMode] = useState(false);\n  const [batchCount, setBatchCount] = useState(3);\n\n  // 思维链相关状态\n  const [currentThinking, setCurrentThinking] = useState('');\n  const [showThinking, setShowThinking] = useState(false);\n  const [thinkingHistory, setThinkingHistory] = useState([]);\n\n  // 获取AI提供商列表\n  const fetchProviders = async () => {\n    try {\n      const response = await axios.get('/api/v1/ai/providers');\n      setProviders(response.data.providers);\n      setCurrentProvider(response.data.current);\n    } catch (error) {\n      message.error('获取AI提供商列表失败');\n    }\n  };\n\n  // 获取AI状态\n  const fetchAIStatus = async () => {\n    try {\n      const response = await axios.get('/api/v1/ai/status');\n      setAiStatus(response.data);\n    } catch (error) {\n      setAiStatus({\n        connected: false,\n        status: 'error'\n      });\n    }\n  };\n\n  // 切换AI提供商\n  const switchProvider = async provider => {\n    try {\n      setLoading(true);\n      await axios.post('/api/v1/ai/switch-provider', {\n        provider\n      });\n      setCurrentProvider(provider);\n      message.success(`已切换到 ${provider}`);\n      await fetchAIStatus();\n    } catch (error) {\n      message.error('切换AI提供商失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 发送聊天消息\n  const sendMessage = async () => {\n    if (!inputMessage.trim()) return;\n    const userMessage = {\n      role: 'user',\n      content: inputMessage\n    };\n    const newMessages = [...messages, userMessage];\n    setMessages(newMessages);\n    setInputMessage('');\n    setLoading(true);\n    try {\n      const response = await axios.post('/api/v1/ai/chat', {\n        messages: newMessages,\n        ...aiParams\n      });\n      const aiMessage = {\n        role: 'assistant',\n        content: response.data.response,\n        thinking: response.data.thinking,\n        timestamp: new Date().toISOString()\n      };\n      setMessages([...newMessages, aiMessage]);\n\n      // 如果有思维过程，保存到历史记录\n      if (response.data.thinking) {\n        setThinkingHistory(prev => [{\n          id: Date.now(),\n          content: response.data.response,\n          thinking: response.data.thinking,\n          timestamp: new Date().toLocaleString(),\n          type: 'chat'\n        }, ...prev]);\n      }\n    } catch (error) {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 503) {\n        message.error('AI服务连接失败，请检查配置和网络连接');\n      } else {\n        var _error$response2, _error$response2$data;\n        message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'AI对话失败');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 生成内容\n  const generateContent = async (type, values, isBatch = false) => {\n    try {\n      setLoading(true);\n      const params = {\n        prompt: values.prompt,\n        max_tokens: values.maxTokens || aiParams.max_tokens,\n        temperature: values.temperature || aiParams.temperature,\n        ...aiParams\n      };\n      if (isBatch && batchMode) {\n        // 批量生成\n        const results = [];\n        for (let i = 0; i < batchCount; i++) {\n          const response = await axios.post(`/api/v1/ai/generate-${type}`, params);\n          results.push({\n            id: Date.now() + i,\n            content: response.data.content,\n            thinking: response.data.thinking,\n            type: type,\n            timestamp: new Date().toLocaleString(),\n            provider: currentProvider\n          });\n\n          // 保存思维过程\n          if (response.data.thinking) {\n            setThinkingHistory(prev => [{\n              id: Date.now() + i + 1000,\n              content: response.data.content,\n              thinking: response.data.thinking,\n              timestamp: new Date().toLocaleString(),\n              type: type\n            }, ...prev]);\n          }\n        }\n\n        // 保存到历史记录\n        setContentHistory(prev => [...results, ...prev]);\n        setGeneratedContent(results[0].content);\n        setCurrentThinking(results[0].thinking || '');\n        message.success(`批量生成${batchCount}个${type}成功`);\n        return results[0].content;\n      } else {\n        // 单个生成\n        const response = await axios.post(`/api/v1/ai/generate-${type}`, params);\n        const result = {\n          id: Date.now(),\n          content: response.data.content,\n          thinking: response.data.thinking,\n          type: type,\n          timestamp: new Date().toLocaleString(),\n          provider: currentProvider\n        };\n\n        // 保存到历史记录\n        setContentHistory(prev => [result, ...prev]);\n        setGeneratedContent(response.data.content);\n        setCurrentThinking(response.data.thinking || '');\n\n        // 保存思维过程\n        if (response.data.thinking) {\n          setThinkingHistory(prev => [{\n            id: Date.now() + 1000,\n            content: response.data.content,\n            thinking: response.data.thinking,\n            timestamp: new Date().toLocaleString(),\n            type: type\n          }, ...prev]);\n        }\n        message.success('内容生成成功');\n        return response.data.content;\n      }\n    } catch (error) {\n      var _error$response3;\n      if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 503) {\n        message.error('AI服务连接失败，请检查配置和网络连接');\n      } else {\n        var _error$response4, _error$response4$data;\n        message.error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || `生成${type}失败`);\n      }\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 保存内容到本地\n  const saveContent = (content, filename) => {\n    const blob = new Blob([content], {\n      type: 'text/plain;charset=utf-8'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename || `ai_content_${Date.now()}.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n    message.success('内容已保存到本地');\n  };\n\n  // 复制内容到剪贴板\n  const copyToClipboard = content => {\n    navigator.clipboard.writeText(content).then(() => {\n      message.success('内容已复制到剪贴板');\n    }).catch(() => {\n      message.error('复制失败');\n    });\n  };\n\n  // 加载模板\n  const loadTemplates = () => {\n    const defaultTemplates = [{\n      id: 1,\n      name: '玄幻世界设定',\n      type: 'setting',\n      content: '创建一个修仙世界，包含多个门派，有完整的修炼体系和等级划分...'\n    }, {\n      id: 2,\n      name: '现代都市背景',\n      type: 'setting',\n      content: '设定一个现代都市背景，包含商业、科技、社会结构...'\n    }, {\n      id: 3,\n      name: '主角人物模板',\n      type: 'character',\n      content: '创建一个年轻的主角，有特殊能力，性格坚韧不拔...'\n    }, {\n      id: 4,\n      name: '反派角色模板',\n      type: 'character',\n      content: '设计一个有深度的反派角色，有合理的动机和背景...'\n    }];\n    setTemplates(defaultTemplates);\n  };\n\n  // 应用模板\n  const applyTemplate = template => {\n    if (template.type === 'setting') {\n      generateForm.setFieldsValue({\n        prompt: template.content\n      });\n      setActiveTab('setting');\n    } else if (template.type === 'character') {\n      generateForm.setFieldsValue({\n        prompt: template.content\n      });\n      setActiveTab('character');\n    }\n    setShowTemplates(false);\n    message.success(`已应用模板：${template.name}`);\n  };\n\n  // 滚动到消息底部\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    fetchProviders();\n    fetchAIStatus();\n    loadTemplates();\n  }, []);\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // 状态指示器\n  const StatusIndicator = () => /*#__PURE__*/_jsxDEV(Space, {\n    children: [/*#__PURE__*/_jsxDEV(Tag, {\n      color: aiStatus.connected ? 'green' : 'red',\n      children: aiStatus.connected ? '在线' : '离线'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: [\"\\u5F53\\u524D\\u63D0\\u4F9B\\u5546: \", currentProvider]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 15\n      }, this),\n      onClick: fetchAIStatus,\n      loading: loading,\n      children: \"\\u5237\\u65B0\\u72B6\\u6001\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 15\n      }, this),\n      onClick: () => setShowHistory(true),\n      children: [\"\\u5386\\u53F2\\u8BB0\\u5F55 \", /*#__PURE__*/_jsxDEV(Badge, {\n        count: contentHistory.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 14\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 15\n      }, this),\n      onClick: () => setShowTemplates(true),\n      children: \"\\u6A21\\u677F\\u5E93\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 15\n      }, this),\n      onClick: () => setShowSettings(true),\n      children: \"\\u9AD8\\u7EA7\\u8BBE\\u7F6E\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(BulbOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 15\n      }, this),\n      onClick: () => setShowThinking(true),\n      type: thinkingHistory.length > 0 ? 'primary' : 'default',\n      children: [\"\\u601D\\u7EF4\\u8FC7\\u7A0B \", /*#__PURE__*/_jsxDEV(Badge, {\n        count: thinkingHistory.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 14\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 349,\n    columnNumber: 5\n  }, this);\n\n  // AI参数设置面板\n  const AISettingsPanel = () => /*#__PURE__*/_jsxDEV(Drawer, {\n    title: \"AI\\u9AD8\\u7EA7\\u53C2\\u6570\\u8BBE\\u7F6E\",\n    placement: \"right\",\n    onClose: () => setShowSettings(false),\n    open: showSettings,\n    width: 400,\n    children: /*#__PURE__*/_jsxDEV(Form, {\n      layout: \"vertical\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6E29\\u5EA6\\u53C2\\u6570 (Temperature)\",\n        children: [/*#__PURE__*/_jsxDEV(Slider, {\n          min: 0,\n          max: 2,\n          step: 0.1,\n          value: aiParams.temperature,\n          onChange: value => setAiParams(prev => ({\n            ...prev,\n            temperature: value\n          })),\n          marks: {\n            0: '保守',\n            0.7: '平衡',\n            1.4: '创新',\n            2: '随机'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u63A7\\u5236\\u8F93\\u51FA\\u7684\\u968F\\u673A\\u6027\\u548C\\u521B\\u9020\\u6027\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6700\\u5927Token\\u6570\",\n        children: [/*#__PURE__*/_jsxDEV(InputNumber, {\n          min: 100,\n          max: 8000,\n          value: aiParams.max_tokens,\n          onChange: value => setAiParams(prev => ({\n            ...prev,\n            max_tokens: value\n          })),\n          style: {\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u63A7\\u5236\\u751F\\u6210\\u5185\\u5BB9\\u7684\\u957F\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"Top P\",\n        children: [/*#__PURE__*/_jsxDEV(Slider, {\n          min: 0,\n          max: 1,\n          step: 0.1,\n          value: aiParams.top_p,\n          onChange: value => setAiParams(prev => ({\n            ...prev,\n            top_p: value\n          })),\n          marks: {\n            0: '0',\n            0.5: '0.5',\n            1: '1'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u63A7\\u5236\\u8BCD\\u6C47\\u9009\\u62E9\\u7684\\u591A\\u6837\\u6027\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u9891\\u7387\\u60E9\\u7F5A (Frequency Penalty)\",\n        children: [/*#__PURE__*/_jsxDEV(Slider, {\n          min: -2,\n          max: 2,\n          step: 0.1,\n          value: aiParams.frequency_penalty,\n          onChange: value => setAiParams(prev => ({\n            ...prev,\n            frequency_penalty: value\n          })),\n          marks: {\n            '-2': '-2',\n            '0': '0',\n            '2': '2'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u51CF\\u5C11\\u91CD\\u590D\\u5185\\u5BB9\\u7684\\u51FA\\u73B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u5B58\\u5728\\u60E9\\u7F5A (Presence Penalty)\",\n        children: [/*#__PURE__*/_jsxDEV(Slider, {\n          min: -2,\n          max: 2,\n          step: 0.1,\n          value: aiParams.presence_penalty,\n          onChange: value => setAiParams(prev => ({\n            ...prev,\n            presence_penalty: value\n          })),\n          marks: {\n            '-2': '-2',\n            '0': '0',\n            '2': '2'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u9F13\\u52B1\\u8BA8\\u8BBA\\u65B0\\u8BDD\\u9898\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6279\\u91CF\\u751F\\u6210\\u6A21\\u5F0F\",\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Switch, {\n            checked: batchMode,\n            onChange: setBatchMode,\n            checkedChildren: \"\\u5F00\\u542F\",\n            unCheckedChildren: \"\\u5173\\u95ED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), batchMode && /*#__PURE__*/_jsxDEV(InputNumber, {\n            min: 2,\n            max: 10,\n            value: batchCount,\n            onChange: setBatchCount,\n            addonBefore: \"\\u751F\\u6210\\u6570\\u91CF\",\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          block: true,\n          onClick: () => {\n            message.success('参数设置已保存');\n            setShowSettings(false);\n          },\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 396,\n    columnNumber: 5\n  }, this);\n\n  // 历史记录面板\n  const HistoryPanel = () => /*#__PURE__*/_jsxDEV(Modal, {\n    title: \"\\u751F\\u6210\\u5386\\u53F2\\u8BB0\\u5F55\",\n    open: showHistory,\n    onCancel: () => setShowHistory(false),\n    width: 800,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      danger: true,\n      onClick: () => {\n        setContentHistory([]);\n        message.success('历史记录已清空');\n      },\n      children: \"\\u6E05\\u7A7A\\u8BB0\\u5F55\"\n    }, \"clear\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      onClick: () => setShowHistory(false),\n      children: \"\\u5173\\u95ED\"\n    }, \"close\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 9\n    }, this)],\n    children: /*#__PURE__*/_jsxDEV(List, {\n      dataSource: contentHistory,\n      renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n        actions: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 23\n          }, this),\n          onClick: () => copyToClipboard(item.content),\n          children: \"\\u590D\\u5236\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 23\n          }, this),\n          onClick: () => saveContent(item.content, `${item.type}_${item.id}.txt`),\n          children: \"\\u4E0B\\u8F7D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n          title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u6761\\u8BB0\\u5F55\\u5417\\uFF1F\",\n          onConfirm: () => {\n            setContentHistory(prev => prev.filter(h => h.id !== item.id));\n            message.success('记录已删除');\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5220\\u9664\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 15\n        }, this)],\n        children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: item.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: item.timestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"green\",\n              children: item.provider\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 17\n          }, this),\n          description: /*#__PURE__*/_jsxDEV(Paragraph, {\n            ellipsis: {\n              rows: 3,\n              expandable: true\n            },\n            style: {\n              marginBottom: 0\n            },\n            children: item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this),\n      locale: {\n        emptyText: '暂无生成记录'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 521,\n    columnNumber: 5\n  }, this);\n\n  // 模板库面板\n  const TemplatePanel = () => /*#__PURE__*/_jsxDEV(Modal, {\n    title: \"\\u6A21\\u677F\\u5E93\",\n    open: showTemplates,\n    onCancel: () => setShowTemplates(false),\n    width: 600,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      onClick: () => setShowTemplates(false),\n      children: \"\\u5173\\u95ED\"\n    }, \"close\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 9\n    }, this)],\n    children: /*#__PURE__*/_jsxDEV(List, {\n      dataSource: templates,\n      renderItem: template => /*#__PURE__*/_jsxDEV(List.Item, {\n        actions: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"small\",\n          onClick: () => applyTemplate(template),\n          children: \"\\u5E94\\u7528\\u6A21\\u677F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 15\n        }, this)],\n        children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: template.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: template.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 17\n          }, this),\n          description: /*#__PURE__*/_jsxDEV(Paragraph, {\n            ellipsis: {\n              rows: 2,\n              expandable: true\n            },\n            style: {\n              marginBottom: 0\n            },\n            children: template.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 596,\n    columnNumber: 5\n  }, this);\n\n  // 思维链面板\n  const ThinkingPanel = () => /*#__PURE__*/_jsxDEV(Modal, {\n    title: \"AI\\u601D\\u7EF4\\u8FC7\\u7A0B\",\n    open: showThinking,\n    onCancel: () => setShowThinking(false),\n    width: 900,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      danger: true,\n      onClick: () => {\n        setThinkingHistory([]);\n        setCurrentThinking('');\n        message.success('思维记录已清空');\n      },\n      children: \"\\u6E05\\u7A7A\\u8BB0\\u5F55\"\n    }, \"clear\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      onClick: () => setShowThinking(false),\n      children: \"\\u5173\\u95ED\"\n    }, \"close\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 658,\n      columnNumber: 9\n    }, this)],\n    children: [currentThinking && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5F53\\u524D\\u601D\\u7EF4\\u8FC7\\u7A0B\",\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 21\n        }, this),\n        onClick: () => copyToClipboard(currentThinking),\n        children: \"\\u590D\\u5236\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 13\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Paragraph, {\n        style: {\n          backgroundColor: '#f0f8ff',\n          padding: '12px',\n          borderRadius: '6px',\n          fontFamily: 'monospace',\n          fontSize: '13px',\n          whiteSpace: 'pre-wrap'\n        },\n        children: currentThinking\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      dataSource: thinkingHistory,\n      renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n        actions: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 23\n          }, this),\n          onClick: () => copyToClipboard(item.thinking),\n          children: \"\\u590D\\u5236\\u601D\\u7EF4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 23\n          }, this),\n          onClick: () => copyToClipboard(item.content),\n          children: \"\\u590D\\u5236\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 703,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 23\n          }, this),\n          onClick: () => saveContent(`思维过程：\\n${item.thinking}\\n\\n生成结果：\\n${item.content}`, `thinking_${item.id}.txt`),\n          children: \"\\u4E0B\\u8F7D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 15\n        }, this)],\n        children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Tag, {\n              color: \"purple\",\n              children: \"\\u601D\\u7EF4\\u94FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: item.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: item.timestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 724,\n            columnNumber: 17\n          }, this),\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u601D\\u7EF4\\u8FC7\\u7A0B\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                ellipsis: {\n                  rows: 3,\n                  expandable: true\n                },\n                style: {\n                  backgroundColor: '#f0f8ff',\n                  padding: '8px',\n                  borderRadius: '4px',\n                  marginTop: 4,\n                  fontFamily: 'monospace',\n                  fontSize: '12px'\n                },\n                children: item.thinking\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u751F\\u6210\\u7ED3\\u679C\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                ellipsis: {\n                  rows: 2,\n                  expandable: true\n                },\n                style: {\n                  marginTop: 4\n                },\n                children: item.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 694,\n        columnNumber: 11\n      }, this),\n      locale: {\n        emptyText: '暂无思维记录'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 691,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 645,\n    columnNumber: 5\n  }, this);\n\n  // 聊天界面\n  const ChatInterface = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '600px',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        overflowY: 'auto',\n        padding: '16px',\n        border: '1px solid #d9d9d9',\n        borderRadius: '6px'\n      },\n      children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          color: '#999',\n          marginTop: '100px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(RobotOutlined, {\n          style: {\n            fontSize: '48px',\n            marginBottom: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F00\\u59CB\\u4E0EAI\\u52A9\\u624B\\u5BF9\\u8BDD\\u5427\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 11\n      }, this) : messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'flex-start'\n          },\n          children: [msg.role === 'user' ? /*#__PURE__*/_jsxDEV(UserOutlined, {\n            style: {\n              marginRight: '8px',\n              marginTop: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(RobotOutlined, {\n            style: {\n              marginRight: '8px',\n              marginTop: '4px',\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: msg.role === 'user' ? '用户' : 'AI助手'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                marginTop: '4px',\n                marginBottom: 0\n              },\n              children: msg.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 19\n            }, this), msg.thinking && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(BulbOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 31\n                }, this),\n                onClick: () => {\n                  setCurrentThinking(msg.thinking);\n                  setShowThinking(true);\n                },\n                children: \"\\u67E5\\u770B\\u601D\\u7EF4\\u8FC7\\u7A0B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 13\n      }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 13\n        }, this), \" \", /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"AI\\u6B63\\u5728\\u601D\\u8003\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 22\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 811,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Input.Group, {\n        compact: true,\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          style: {\n            width: 'calc(100% - 80px)'\n          },\n          placeholder: \"\\u8F93\\u5165\\u60A8\\u7684\\u95EE\\u9898...\",\n          value: inputMessage,\n          onChange: e => setInputMessage(e.target.value),\n          onPressEnter: sendMessage,\n          disabled: loading || !aiStatus.connected\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 820,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 19\n          }, this),\n          onClick: sendMessage,\n          loading: loading,\n          disabled: !aiStatus.connected,\n          children: \"\\u53D1\\u9001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 819,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 818,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 769,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"AI\\u52A9\\u624B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 845,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 846,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 844,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 18,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            activeKey: activeTab,\n            onChange: setActiveTab,\n            children: [/*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 35\n                }, this), \"\\u667A\\u80FD\\u5BF9\\u8BDD\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ChatInterface, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 17\n              }, this)\n            }, \"chat\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(BulbOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 857,\n                  columnNumber: 35\n                }, this), \"\\u8BBE\\u5B9A\\u751F\\u6210\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: generateForm,\n                layout: \"vertical\",\n                onFinish: values => generateContent('setting', values, batchMode),\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"prompt\",\n                  label: \"\\u751F\\u6210\\u8981\\u6C42\",\n                  rules: [{\n                    required: true,\n                    message: '请输入生成要求'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 4,\n                    placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u60A8\\u60F3\\u8981\\u751F\\u6210\\u7684\\u4E16\\u754C\\u8BBE\\u5B9A\\uFF0C\\u4F8B\\u5982\\uFF1A\\u4E00\\u4E2A\\u4FEE\\u4ED9\\u4E16\\u754C\\uFF0C\\u6709\\u591A\\u4E2A\\u95E8\\u6D3E...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 868,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"maxTokens\",\n                      label: \"\\u6700\\u5927\\u5B57\\u6570\",\n                      initialValue: 2000,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 1000,\n                          children: \"1000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 878,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 2000,\n                          children: \"2000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 879,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 3000,\n                          children: \"3000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 880,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 877,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 876,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 875,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"temperature\",\n                      label: \"\\u521B\\u610F\\u5EA6\",\n                      initialValue: 0.7,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.3,\n                          children: \"\\u4FDD\\u5B88\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 887,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.7,\n                          children: \"\\u5E73\\u8861\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 888,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.9,\n                          children: \"\\u521B\\u65B0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 889,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 886,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 885,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 884,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      loading: loading,\n                      disabled: !aiStatus.connected,\n                      icon: batchMode ? /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 902,\n                        columnNumber: 43\n                      }, this) : /*#__PURE__*/_jsxDEV(BulbOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 902,\n                        columnNumber: 69\n                      }, this),\n                      children: batchMode ? `批量生成(${batchCount}个)` : '生成世界设定'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 897,\n                      columnNumber: 23\n                    }, this), generatedContent && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 909,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => copyToClipboard(generatedContent),\n                        children: \"\\u590D\\u5236\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 908,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 915,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => saveContent(generatedContent, 'setting.txt'),\n                        children: \"\\u4E0B\\u8F7D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 914,\n                        columnNumber: 27\n                      }, this), currentThinking && /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(BulbOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 922,\n                          columnNumber: 37\n                        }, this),\n                        onClick: () => setShowThinking(true),\n                        children: \"\\u601D\\u7EF4\\u8FC7\\u7A0B\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 921,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 896,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 895,\n                  columnNumber: 19\n                }, this), generatedContent && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u751F\\u6210\\u7ED3\\u679C\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    value: generatedContent,\n                    rows: 8,\n                    readOnly: true,\n                    style: {\n                      backgroundColor: '#f5f5f5'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 935,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 934,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 17\n              }, this)\n            }, \"setting\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 35\n                }, this), \"\\u4EBA\\u7269\\u751F\\u6210\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                layout: \"vertical\",\n                onFinish: values => generateContent('character', values, batchMode),\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"prompt\",\n                  label: \"\\u4EBA\\u7269\\u8981\\u6C42\",\n                  rules: [{\n                    required: true,\n                    message: '请输入人物要求'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 4,\n                    placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u60A8\\u60F3\\u8981\\u751F\\u6210\\u7684\\u4EBA\\u7269\\uFF0C\\u4F8B\\u5982\\uFF1A\\u4E00\\u4E2A\\u5E74\\u8F7B\\u7684\\u5251\\u4FEE\\uFF0C\\u6027\\u683C\\u51B7\\u50B2...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 956,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 951,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"maxTokens\",\n                      label: \"\\u6700\\u5927\\u5B57\\u6570\",\n                      initialValue: 2000,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 1000,\n                          children: \"1000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 966,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 2000,\n                          children: \"2000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 967,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 3000,\n                          children: \"3000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 968,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 965,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 964,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 963,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"temperature\",\n                      label: \"\\u521B\\u610F\\u5EA6\",\n                      initialValue: 0.7,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.3,\n                          children: \"\\u4FDD\\u5B88\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 975,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.7,\n                          children: \"\\u5E73\\u8861\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 976,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.9,\n                          children: \"\\u521B\\u65B0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 977,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 974,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 973,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      loading: loading,\n                      disabled: !aiStatus.connected,\n                      icon: batchMode ? /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 990,\n                        columnNumber: 43\n                      }, this) : /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 990,\n                        columnNumber: 69\n                      }, this),\n                      children: batchMode ? `批量生成(${batchCount}个)` : '生成人物设定'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 23\n                    }, this), generatedContent && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 997,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => copyToClipboard(generatedContent),\n                        children: \"\\u590D\\u5236\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 996,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1003,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => saveContent(generatedContent, 'character.txt'),\n                        children: \"\\u4E0B\\u8F7D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1002,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 19\n                }, this), generatedContent && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u751F\\u6210\\u7ED3\\u679C\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    value: generatedContent,\n                    rows: 8,\n                    readOnly: true,\n                    style: {\n                      backgroundColor: '#f5f5f5'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1014,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 17\n              }, this)\n            }, \"character\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1026,\n                  columnNumber: 35\n                }, this), \"\\u5267\\u60C5\\u751F\\u6210\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: plotForm,\n                layout: \"vertical\",\n                onFinish: values => generateContent('plot', values, batchMode),\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"prompt\",\n                  label: \"\\u5267\\u60C5\\u8981\\u6C42\",\n                  rules: [{\n                    required: true,\n                    message: '请输入剧情要求'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 4,\n                    placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u60A8\\u60F3\\u8981\\u751F\\u6210\\u7684\\u5267\\u60C5\\uFF0C\\u4F8B\\u5982\\uFF1A\\u4E3B\\u89D2\\u5728\\u4FEE\\u4ED9\\u95E8\\u6D3E\\u4E2D\\u9047\\u5230\\u7684\\u7B2C\\u4E00\\u4E2A\\u6311\\u6218...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1032,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"maxTokens\",\n                      label: \"\\u6700\\u5927\\u5B57\\u6570\",\n                      initialValue: 3000,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 2000,\n                          children: \"2000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1047,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 3000,\n                          children: \"3000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1048,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 5000,\n                          children: \"5000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1049,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1046,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1045,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1044,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"temperature\",\n                      label: \"\\u521B\\u610F\\u5EA6\",\n                      initialValue: 0.8,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.5,\n                          children: \"\\u4FDD\\u5B88\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1056,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.8,\n                          children: \"\\u5E73\\u8861\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1057,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 1.0,\n                          children: \"\\u521B\\u65B0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1058,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1055,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1054,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"plotType\",\n                      label: \"\\u5267\\u60C5\\u7C7B\\u578B\",\n                      initialValue: \"adventure\",\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: \"adventure\",\n                          children: \"\\u5192\\u9669\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1065,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: \"romance\",\n                          children: \"\\u7231\\u60C5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1066,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: \"conflict\",\n                          children: \"\\u51B2\\u7A81\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1067,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: \"mystery\",\n                          children: \"\\u60AC\\u7591\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1068,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1064,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1063,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1062,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1043,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      loading: loading,\n                      disabled: !aiStatus.connected,\n                      icon: batchMode ? /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1081,\n                        columnNumber: 43\n                      }, this) : /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1081,\n                        columnNumber: 69\n                      }, this),\n                      children: batchMode ? `批量生成(${batchCount}个)` : '生成剧情大纲'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1076,\n                      columnNumber: 23\n                    }, this), generatedContent && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1088,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => copyToClipboard(generatedContent),\n                        children: \"\\u590D\\u5236\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1087,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1094,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => saveContent(generatedContent, 'plot.txt'),\n                        children: \"\\u4E0B\\u8F7D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1093,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1075,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1074,\n                  columnNumber: 19\n                }, this), generatedContent && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u751F\\u6210\\u7ED3\\u679C\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    value: generatedContent,\n                    rows: 8,\n                    readOnly: true,\n                    style: {\n                      backgroundColor: '#f5f5f5'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1106,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 17\n              }, this)\n            }, \"plot\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1117,\n                  columnNumber: 35\n                }, this), \"\\u7EED\\u5199\\u529F\\u80FD\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1117,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: continueForm,\n                layout: \"vertical\",\n                onFinish: values => {\n                  const prompt = values.continueHint ? `${values.prompt}\\n\\n续写提示：${values.continueHint}` : values.prompt;\n                  generateContent('continue-writing', {\n                    ...values,\n                    prompt\n                  });\n                },\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"prompt\",\n                  label: \"\\u539F\\u6587\\u5185\\u5BB9\",\n                  rules: [{\n                    required: true,\n                    message: '请输入需要续写的原文内容'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 6,\n                    placeholder: \"\\u8BF7\\u7C98\\u8D34\\u9700\\u8981\\u7EED\\u5199\\u7684\\u539F\\u6587\\u5185\\u5BB9...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1133,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1128,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"continueHint\",\n                  label: \"\\u7EED\\u5199\\u63D0\\u793A\\uFF08\\u53EF\\u9009\\uFF09\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 2,\n                    placeholder: \"\\u53EF\\u4EE5\\u63D0\\u4F9B\\u7EED\\u5199\\u7684\\u65B9\\u5411\\u63D0\\u793A\\uFF0C\\u4F8B\\u5982\\uFF1A\\u63A5\\u4E0B\\u6765\\u4E3B\\u89D2\\u9047\\u5230\\u4E86...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1143,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"maxTokens\",\n                      label: \"\\u7EED\\u5199\\u957F\\u5EA6\",\n                      initialValue: 2000,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 1000,\n                          children: \"\\u77ED\\u7BC7(1000\\u5B57)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1153,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 2000,\n                          children: \"\\u4E2D\\u7BC7(2000\\u5B57)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1154,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 3000,\n                          children: \"\\u957F\\u7BC7(3000\\u5B57)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1155,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1152,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1151,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1150,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"temperature\",\n                      label: \"\\u521B\\u610F\\u5EA6\",\n                      initialValue: 0.7,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.5,\n                          children: \"\\u4FDD\\u5B88\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1162,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.7,\n                          children: \"\\u5E73\\u8861\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1163,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.9,\n                          children: \"\\u521B\\u65B0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1164,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1161,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1160,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1159,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1149,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      loading: loading,\n                      disabled: !aiStatus.connected,\n                      icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1177,\n                        columnNumber: 31\n                      }, this),\n                      children: \"AI\\u7EED\\u5199\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1172,\n                      columnNumber: 23\n                    }, this), generatedContent && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1184,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => copyToClipboard(generatedContent),\n                        children: \"\\u590D\\u5236\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1183,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1190,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => saveContent(generatedContent, 'continue.txt'),\n                        children: \"\\u4E0B\\u8F7D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1189,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1171,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1170,\n                  columnNumber: 19\n                }, this), generatedContent && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u7EED\\u5199\\u7ED3\\u679C\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    value: generatedContent,\n                    rows: 8,\n                    readOnly: true,\n                    style: {\n                      backgroundColor: '#f5f5f5'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1202,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1201,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1118,\n                columnNumber: 17\n              }, this)\n            }, \"continue\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1213,\n                  columnNumber: 35\n                }, this), \"\\u4E00\\u81F4\\u6027\\u68C0\\u67E5\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1213,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: checkForm,\n                layout: \"vertical\",\n                onFinish: values => {\n                  const prompt = `检查类型：${values.checkType}\\n\\n内容：\\n${values.prompt}`;\n                  generateContent('check-consistency', {\n                    ...values,\n                    prompt\n                  });\n                },\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"prompt\",\n                  label: \"\\u68C0\\u67E5\\u5185\\u5BB9\",\n                  rules: [{\n                    required: true,\n                    message: '请输入需要检查的内容'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 8,\n                    placeholder: \"\\u8BF7\\u7C98\\u8D34\\u9700\\u8981\\u8FDB\\u884C\\u4E00\\u81F4\\u6027\\u68C0\\u67E5\\u7684\\u5C0F\\u8BF4\\u5185\\u5BB9...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1227,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"checkType\",\n                  label: \"\\u68C0\\u67E5\\u7C7B\\u578B\",\n                  initialValue: \"all\",\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    children: [/*#__PURE__*/_jsxDEV(Option, {\n                      value: \"all\",\n                      children: \"\\u5168\\u9762\\u68C0\\u67E5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1239,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"character\",\n                      children: \"\\u4EBA\\u7269\\u4E00\\u81F4\\u6027\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1240,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"plot\",\n                      children: \"\\u60C5\\u8282\\u903B\\u8F91\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1241,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"setting\",\n                      children: \"\\u8BBE\\u5B9A\\u4E00\\u81F4\\u6027\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1242,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"timeline\",\n                      children: \"\\u65F6\\u95F4\\u7EBF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1243,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1238,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      loading: loading,\n                      disabled: !aiStatus.connected,\n                      icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1254,\n                        columnNumber: 31\n                      }, this),\n                      children: \"\\u5F00\\u59CB\\u68C0\\u67E5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1249,\n                      columnNumber: 23\n                    }, this), generatedContent && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1261,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => copyToClipboard(generatedContent),\n                        children: \"\\u590D\\u5236\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1260,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1267,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => saveContent(generatedContent, 'check_report.txt'),\n                        children: \"\\u4E0B\\u8F7D\\u62A5\\u544A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1266,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1248,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1247,\n                  columnNumber: 19\n                }, this), generatedContent && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u68C0\\u67E5\\u62A5\\u544A\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    value: generatedContent,\n                    rows: 8,\n                    readOnly: true,\n                    style: {\n                      backgroundColor: '#f5f5f5'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1279,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1278,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1214,\n                columnNumber: 17\n              }, this)\n            }, \"check\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 1213,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"AI\\u8BBE\\u7F6E\",\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"AI\\u63D0\\u4F9B\\u5546\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              style: {\n                width: '100%',\n                marginTop: '8px'\n              },\n              value: currentProvider,\n              onChange: switchProvider,\n              loading: loading,\n              children: providers.map(provider => /*#__PURE__*/_jsxDEV(Option, {\n                value: provider,\n                children: provider.toUpperCase()\n              }, provider, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1304,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u5FEB\\u901F\\u64CD\\u4F5C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                block: true,\n                style: {\n                  marginBottom: '8px'\n                },\n                onClick: () => setMessages([]),\n                children: \"\\u6E05\\u7A7A\\u5BF9\\u8BDD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1316,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                block: true,\n                onClick: fetchAIStatus,\n                loading: loading,\n                children: \"\\u68C0\\u67E5\\u8FDE\\u63A5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1294,\n          columnNumber: 11\n        }, this), !aiStatus.connected && /*#__PURE__*/_jsxDEV(Alert, {\n          style: {\n            marginTop: '16px'\n          },\n          message: \"AI\\u670D\\u52A1\\u79BB\\u7EBF\",\n          description: \"\\u8BF7\\u68C0\\u67E5AI\\u670D\\u52A1\\u914D\\u7F6E\\u6216\\u7F51\\u7EDC\\u8FDE\\u63A5\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1335,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 849,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AISettingsPanel, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1347,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HistoryPanel, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TemplatePanel, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1349,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ThinkingPanel, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1350,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 843,\n    columnNumber: 5\n  }, this);\n};\n_s(AIAssistant, \"0AN1JHBuVT3xT1xeMNx38qM7TC4=\", false, function () {\n  return [Form.useForm, Form.useForm, Form.useForm, Form.useForm];\n});\n_c = AIAssistant;\nexport default AIAssistant;\nvar _c;\n$RefreshReg$(_c, \"AIAssistant\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Card", "Typography", "<PERSON><PERSON>", "Input", "Select", "Row", "Col", "Tabs", "Form", "message", "Spin", "Space", "Tag", "Divider", "<PERSON><PERSON>", "Modal", "List", "Slide<PERSON>", "Switch", "InputNumber", "Badge", "Popconfirm", "Drawer", "RobotOutlined", "MessageOutlined", "SendOutlined", "BulbOutlined", "UserOutlined", "ReloadOutlined", "BookOutlined", "EditOutlined", "CheckCircleOutlined", "HistoryOutlined", "DownloadOutlined", "CopyOutlined", "DeleteOutlined", "SettingOutlined", "FileTextOutlined", "ThunderboltOutlined", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Paragraph", "TextArea", "Option", "TabPane", "AIAssistant", "_s", "loading", "setLoading", "providers", "setProviders", "currentProvider", "setCurrentProvider", "aiStatus", "setAiStatus", "connected", "status", "messages", "setMessages", "inputMessage", "setInputMessage", "generateForm", "useForm", "plotForm", "continueForm", "checkForm", "activeTab", "setActiveTab", "messagesEndRef", "generatedContent", "setGeneratedContent", "contentHistory", "setContentHistory", "showHistory", "setShowHistory", "showTemplates", "setShowTemplates", "showSettings", "setShowSettings", "templates", "setTemplates", "aiParams", "setAiParams", "temperature", "max_tokens", "top_p", "frequency_penalty", "presence_penalty", "batchMode", "setBatchMode", "batchCount", "setBatchCount", "currentThinking", "setCurrentThinking", "showThinking", "setShowThinking", "thinkingHistory", "setThinkingHistory", "fetchProviders", "response", "get", "data", "current", "error", "fetchAIStatus", "switchProvider", "provider", "post", "success", "sendMessage", "trim", "userMessage", "role", "content", "newMessages", "aiMessage", "thinking", "timestamp", "Date", "toISOString", "prev", "id", "now", "toLocaleString", "type", "_error$response", "_error$response2", "_error$response2$data", "detail", "generateContent", "values", "isBatch", "params", "prompt", "maxTokens", "results", "i", "push", "result", "_error$response3", "_error$response4", "_error$response4$data", "saveContent", "filename", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "copyToClipboard", "navigator", "clipboard", "writeText", "then", "catch", "loadTemplates", "defaultTemplates", "name", "applyTemplate", "template", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scrollToBottom", "_messagesEndRef$curre", "scrollIntoView", "behavior", "StatusIndicator", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "icon", "onClick", "count", "length", "AISettingsPanel", "title", "placement", "onClose", "open", "width", "layout", "<PERSON><PERSON>", "label", "min", "max", "step", "value", "onChange", "marks", "style", "direction", "checked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "addonBefore", "block", "HistoryPanel", "onCancel", "footer", "danger", "dataSource", "renderItem", "item", "actions", "onConfirm", "filter", "h", "Meta", "description", "ellipsis", "rows", "expandable", "marginBottom", "locale", "emptyText", "TemplatePanel", "strong", "ThinkingPanel", "extra", "backgroundColor", "padding", "borderRadius", "fontFamily", "fontSize", "whiteSpace", "marginTop", "ChatInterface", "height", "display", "flexDirection", "flex", "overflowY", "border", "textAlign", "map", "msg", "index", "alignItems", "marginRight", "ref", "Group", "compact", "placeholder", "e", "target", "onPressEnter", "disabled", "className", "level", "gutter", "span", "active<PERSON><PERSON>", "tab", "form", "onFinish", "rules", "required", "initialValue", "htmlType", "readOnly", "continueHint", "checkType", "toUpperCase", "showIcon", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/AIAssistant.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Input,\n  Select,\n  Row,\n  Col,\n  Tabs,\n  Form,\n  message,\n  Spin,\n  Space,\n  Tag,\n  Divider,\n  Alert,\n  Modal,\n  List,\n\n  Slider,\n  Switch,\n  Input<PERSON><PERSON>ber,\n\n  Badge,\n  Popconfirm,\n\n  Drawer\n} from 'antd';\nimport {\n  RobotOutlined,\n  MessageOutlined,\n  SendOutlined,\n  BulbOutlined,\n  UserOutlined,\n  ReloadOutlined,\n  BookOutlined,\n  EditOutlined,\n  CheckCircleOutlined,\n\n  HistoryOutlined,\n\n  DownloadOutlined,\n\n  CopyOutlined,\n  DeleteOutlined,\n  SettingOutlined,\n\n  FileTextOutlined,\n  ThunderboltOutlined\n} from '@ant-design/icons';\nimport axios from 'axios';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { TabPane } = Tabs;\n\n\nconst AIAssistant = () => {\n  const [loading, setLoading] = useState(false);\n  const [providers, setProviders] = useState([]);\n  const [currentProvider, setCurrentProvider] = useState('');\n  const [aiStatus, setAiStatus] = useState({ connected: false, status: 'offline' });\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [generateForm] = Form.useForm();\n  const [plotForm] = Form.useForm();\n  const [continueForm] = Form.useForm();\n  const [checkForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('chat');\n  const messagesEndRef = useRef(null);\n\n  // 新增状态\n  const [generatedContent, setGeneratedContent] = useState('');\n  const [contentHistory, setContentHistory] = useState([]);\n  const [showHistory, setShowHistory] = useState(false);\n  const [showTemplates, setShowTemplates] = useState(false);\n  const [showSettings, setShowSettings] = useState(false);\n  const [templates, setTemplates] = useState([]);\n  const [aiParams, setAiParams] = useState({\n    temperature: 0.7,\n    max_tokens: 2000,\n    top_p: 1.0,\n    frequency_penalty: 0.0,\n    presence_penalty: 0.0\n  });\n  const [batchMode, setBatchMode] = useState(false);\n  const [batchCount, setBatchCount] = useState(3);\n\n  // 思维链相关状态\n  const [currentThinking, setCurrentThinking] = useState('');\n  const [showThinking, setShowThinking] = useState(false);\n  const [thinkingHistory, setThinkingHistory] = useState([]);\n\n  // 获取AI提供商列表\n  const fetchProviders = async () => {\n    try {\n      const response = await axios.get('/api/v1/ai/providers');\n      setProviders(response.data.providers);\n      setCurrentProvider(response.data.current);\n    } catch (error) {\n      message.error('获取AI提供商列表失败');\n    }\n  };\n\n  // 获取AI状态\n  const fetchAIStatus = async () => {\n    try {\n      const response = await axios.get('/api/v1/ai/status');\n      setAiStatus(response.data);\n    } catch (error) {\n      setAiStatus({ connected: false, status: 'error' });\n    }\n  };\n\n  // 切换AI提供商\n  const switchProvider = async (provider) => {\n    try {\n      setLoading(true);\n      await axios.post('/api/v1/ai/switch-provider', { provider });\n      setCurrentProvider(provider);\n      message.success(`已切换到 ${provider}`);\n      await fetchAIStatus();\n    } catch (error) {\n      message.error('切换AI提供商失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 发送聊天消息\n  const sendMessage = async () => {\n    if (!inputMessage.trim()) return;\n\n    const userMessage = { role: 'user', content: inputMessage };\n    const newMessages = [...messages, userMessage];\n    setMessages(newMessages);\n    setInputMessage('');\n    setLoading(true);\n\n    try {\n      const response = await axios.post('/api/v1/ai/chat', {\n        messages: newMessages,\n        ...aiParams\n      });\n\n      const aiMessage = {\n        role: 'assistant',\n        content: response.data.response,\n        thinking: response.data.thinking,\n        timestamp: new Date().toISOString()\n      };\n\n      setMessages([...newMessages, aiMessage]);\n\n      // 如果有思维过程，保存到历史记录\n      if (response.data.thinking) {\n        setThinkingHistory(prev => [{\n          id: Date.now(),\n          content: response.data.response,\n          thinking: response.data.thinking,\n          timestamp: new Date().toLocaleString(),\n          type: 'chat'\n        }, ...prev]);\n      }\n\n    } catch (error) {\n      if (error.response?.status === 503) {\n        message.error('AI服务连接失败，请检查配置和网络连接');\n      } else {\n        message.error(error.response?.data?.detail || 'AI对话失败');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 生成内容\n  const generateContent = async (type, values, isBatch = false) => {\n    try {\n      setLoading(true);\n      const params = {\n        prompt: values.prompt,\n        max_tokens: values.maxTokens || aiParams.max_tokens,\n        temperature: values.temperature || aiParams.temperature,\n        ...aiParams\n      };\n\n      if (isBatch && batchMode) {\n        // 批量生成\n        const results = [];\n        for (let i = 0; i < batchCount; i++) {\n          const response = await axios.post(`/api/v1/ai/generate-${type}`, params);\n          results.push({\n            id: Date.now() + i,\n            content: response.data.content,\n            thinking: response.data.thinking,\n            type: type,\n            timestamp: new Date().toLocaleString(),\n            provider: currentProvider\n          });\n\n          // 保存思维过程\n          if (response.data.thinking) {\n            setThinkingHistory(prev => [{\n              id: Date.now() + i + 1000,\n              content: response.data.content,\n              thinking: response.data.thinking,\n              timestamp: new Date().toLocaleString(),\n              type: type\n            }, ...prev]);\n          }\n        }\n\n        // 保存到历史记录\n        setContentHistory(prev => [...results, ...prev]);\n        setGeneratedContent(results[0].content);\n        setCurrentThinking(results[0].thinking || '');\n        message.success(`批量生成${batchCount}个${type}成功`);\n        return results[0].content;\n      } else {\n        // 单个生成\n        const response = await axios.post(`/api/v1/ai/generate-${type}`, params);\n        const result = {\n          id: Date.now(),\n          content: response.data.content,\n          thinking: response.data.thinking,\n          type: type,\n          timestamp: new Date().toLocaleString(),\n          provider: currentProvider\n        };\n\n        // 保存到历史记录\n        setContentHistory(prev => [result, ...prev]);\n        setGeneratedContent(response.data.content);\n        setCurrentThinking(response.data.thinking || '');\n\n        // 保存思维过程\n        if (response.data.thinking) {\n          setThinkingHistory(prev => [{\n            id: Date.now() + 1000,\n            content: response.data.content,\n            thinking: response.data.thinking,\n            timestamp: new Date().toLocaleString(),\n            type: type\n          }, ...prev]);\n        }\n\n        message.success('内容生成成功');\n        return response.data.content;\n      }\n    } catch (error) {\n      if (error.response?.status === 503) {\n        message.error('AI服务连接失败，请检查配置和网络连接');\n      } else {\n        message.error(error.response?.data?.detail || `生成${type}失败`);\n      }\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 保存内容到本地\n  const saveContent = (content, filename) => {\n    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename || `ai_content_${Date.now()}.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n    message.success('内容已保存到本地');\n  };\n\n  // 复制内容到剪贴板\n  const copyToClipboard = (content) => {\n    navigator.clipboard.writeText(content).then(() => {\n      message.success('内容已复制到剪贴板');\n    }).catch(() => {\n      message.error('复制失败');\n    });\n  };\n\n  // 加载模板\n  const loadTemplates = () => {\n    const defaultTemplates = [\n      {\n        id: 1,\n        name: '玄幻世界设定',\n        type: 'setting',\n        content: '创建一个修仙世界，包含多个门派，有完整的修炼体系和等级划分...'\n      },\n      {\n        id: 2,\n        name: '现代都市背景',\n        type: 'setting',\n        content: '设定一个现代都市背景，包含商业、科技、社会结构...'\n      },\n      {\n        id: 3,\n        name: '主角人物模板',\n        type: 'character',\n        content: '创建一个年轻的主角，有特殊能力，性格坚韧不拔...'\n      },\n      {\n        id: 4,\n        name: '反派角色模板',\n        type: 'character',\n        content: '设计一个有深度的反派角色，有合理的动机和背景...'\n      }\n    ];\n    setTemplates(defaultTemplates);\n  };\n\n  // 应用模板\n  const applyTemplate = (template) => {\n    if (template.type === 'setting') {\n      generateForm.setFieldsValue({ prompt: template.content });\n      setActiveTab('setting');\n    } else if (template.type === 'character') {\n      generateForm.setFieldsValue({ prompt: template.content });\n      setActiveTab('character');\n    }\n    setShowTemplates(false);\n    message.success(`已应用模板：${template.name}`);\n  };\n\n  // 滚动到消息底部\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    fetchProviders();\n    fetchAIStatus();\n    loadTemplates();\n  }, []);\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // 状态指示器\n  const StatusIndicator = () => (\n    <Space>\n      <Tag color={aiStatus.connected ? 'green' : 'red'}>\n        {aiStatus.connected ? '在线' : '离线'}\n      </Tag>\n      <Text type=\"secondary\">当前提供商: {currentProvider}</Text>\n      <Button\n        size=\"small\"\n        icon={<ReloadOutlined />}\n        onClick={fetchAIStatus}\n        loading={loading}\n      >\n        刷新状态\n      </Button>\n      <Button\n        size=\"small\"\n        icon={<HistoryOutlined />}\n        onClick={() => setShowHistory(true)}\n      >\n        历史记录 <Badge count={contentHistory.length} />\n      </Button>\n      <Button\n        size=\"small\"\n        icon={<FileTextOutlined />}\n        onClick={() => setShowTemplates(true)}\n      >\n        模板库\n      </Button>\n      <Button\n        size=\"small\"\n        icon={<SettingOutlined />}\n        onClick={() => setShowSettings(true)}\n      >\n        高级设置\n      </Button>\n      <Button\n        size=\"small\"\n        icon={<BulbOutlined />}\n        onClick={() => setShowThinking(true)}\n        type={thinkingHistory.length > 0 ? 'primary' : 'default'}\n      >\n        思维过程 <Badge count={thinkingHistory.length} />\n      </Button>\n    </Space>\n  );\n\n  // AI参数设置面板\n  const AISettingsPanel = () => (\n    <Drawer\n      title=\"AI高级参数设置\"\n      placement=\"right\"\n      onClose={() => setShowSettings(false)}\n      open={showSettings}\n      width={400}\n    >\n      <Form layout=\"vertical\">\n        <Form.Item label=\"温度参数 (Temperature)\">\n          <Slider\n            min={0}\n            max={2}\n            step={0.1}\n            value={aiParams.temperature}\n            onChange={(value) => setAiParams(prev => ({ ...prev, temperature: value }))}\n            marks={{\n              0: '保守',\n              0.7: '平衡',\n              1.4: '创新',\n              2: '随机'\n            }}\n          />\n          <Text type=\"secondary\">控制输出的随机性和创造性</Text>\n        </Form.Item>\n\n        <Form.Item label=\"最大Token数\">\n          <InputNumber\n            min={100}\n            max={8000}\n            value={aiParams.max_tokens}\n            onChange={(value) => setAiParams(prev => ({ ...prev, max_tokens: value }))}\n            style={{ width: '100%' }}\n          />\n          <Text type=\"secondary\">控制生成内容的长度</Text>\n        </Form.Item>\n\n        <Form.Item label=\"Top P\">\n          <Slider\n            min={0}\n            max={1}\n            step={0.1}\n            value={aiParams.top_p}\n            onChange={(value) => setAiParams(prev => ({ ...prev, top_p: value }))}\n            marks={{\n              0: '0',\n              0.5: '0.5',\n              1: '1'\n            }}\n          />\n          <Text type=\"secondary\">控制词汇选择的多样性</Text>\n        </Form.Item>\n\n        <Form.Item label=\"频率惩罚 (Frequency Penalty)\">\n          <Slider\n            min={-2}\n            max={2}\n            step={0.1}\n            value={aiParams.frequency_penalty}\n            onChange={(value) => setAiParams(prev => ({ ...prev, frequency_penalty: value }))}\n            marks={{\n              '-2': '-2',\n              '0': '0',\n              '2': '2'\n            }}\n          />\n          <Text type=\"secondary\">减少重复内容的出现</Text>\n        </Form.Item>\n\n        <Form.Item label=\"存在惩罚 (Presence Penalty)\">\n          <Slider\n            min={-2}\n            max={2}\n            step={0.1}\n            value={aiParams.presence_penalty}\n            onChange={(value) => setAiParams(prev => ({ ...prev, presence_penalty: value }))}\n            marks={{\n              '-2': '-2',\n              '0': '0',\n              '2': '2'\n            }}\n          />\n          <Text type=\"secondary\">鼓励讨论新话题</Text>\n        </Form.Item>\n\n        <Divider />\n\n        <Form.Item label=\"批量生成模式\">\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            <Switch\n              checked={batchMode}\n              onChange={setBatchMode}\n              checkedChildren=\"开启\"\n              unCheckedChildren=\"关闭\"\n            />\n            {batchMode && (\n              <InputNumber\n                min={2}\n                max={10}\n                value={batchCount}\n                onChange={setBatchCount}\n                addonBefore=\"生成数量\"\n                style={{ width: '100%' }}\n              />\n            )}\n          </Space>\n        </Form.Item>\n\n        <Form.Item>\n          <Button\n            type=\"primary\"\n            block\n            onClick={() => {\n              message.success('参数设置已保存');\n              setShowSettings(false);\n            }}\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Drawer>\n  );\n\n  // 历史记录面板\n  const HistoryPanel = () => (\n    <Modal\n      title=\"生成历史记录\"\n      open={showHistory}\n      onCancel={() => setShowHistory(false)}\n      width={800}\n      footer={[\n        <Button key=\"clear\" danger onClick={() => {\n          setContentHistory([]);\n          message.success('历史记录已清空');\n        }}>\n          清空记录\n        </Button>,\n        <Button key=\"close\" onClick={() => setShowHistory(false)}>\n          关闭\n        </Button>\n      ]}\n    >\n      <List\n        dataSource={contentHistory}\n        renderItem={(item) => (\n          <List.Item\n            actions={[\n              <Button\n                size=\"small\"\n                icon={<CopyOutlined />}\n                onClick={() => copyToClipboard(item.content)}\n              >\n                复制\n              </Button>,\n              <Button\n                size=\"small\"\n                icon={<DownloadOutlined />}\n                onClick={() => saveContent(item.content, `${item.type}_${item.id}.txt`)}\n              >\n                下载\n              </Button>,\n              <Popconfirm\n                title=\"确定删除这条记录吗？\"\n                onConfirm={() => {\n                  setContentHistory(prev => prev.filter(h => h.id !== item.id));\n                  message.success('记录已删除');\n                }}\n              >\n                <Button size=\"small\" danger icon={<DeleteOutlined />}>\n                  删除\n                </Button>\n              </Popconfirm>\n            ]}\n          >\n            <List.Item.Meta\n              title={\n                <Space>\n                  <Tag color=\"blue\">{item.type}</Tag>\n                  <Text>{item.timestamp}</Text>\n                  <Tag color=\"green\">{item.provider}</Tag>\n                </Space>\n              }\n              description={\n                <Paragraph\n                  ellipsis={{ rows: 3, expandable: true }}\n                  style={{ marginBottom: 0 }}\n                >\n                  {item.content}\n                </Paragraph>\n              }\n            />\n          </List.Item>\n        )}\n        locale={{ emptyText: '暂无生成记录' }}\n      />\n    </Modal>\n  );\n\n  // 模板库面板\n  const TemplatePanel = () => (\n    <Modal\n      title=\"模板库\"\n      open={showTemplates}\n      onCancel={() => setShowTemplates(false)}\n      width={600}\n      footer={[\n        <Button key=\"close\" onClick={() => setShowTemplates(false)}>\n          关闭\n        </Button>\n      ]}\n    >\n      <List\n        dataSource={templates}\n        renderItem={(template) => (\n          <List.Item\n            actions={[\n              <Button\n                type=\"primary\"\n                size=\"small\"\n                onClick={() => applyTemplate(template)}\n              >\n                应用模板\n              </Button>\n            ]}\n          >\n            <List.Item.Meta\n              title={\n                <Space>\n                  <Text strong>{template.name}</Text>\n                  <Tag color=\"blue\">{template.type}</Tag>\n                </Space>\n              }\n              description={\n                <Paragraph\n                  ellipsis={{ rows: 2, expandable: true }}\n                  style={{ marginBottom: 0 }}\n                >\n                  {template.content}\n                </Paragraph>\n              }\n            />\n          </List.Item>\n        )}\n      />\n    </Modal>\n  );\n\n  // 思维链面板\n  const ThinkingPanel = () => (\n    <Modal\n      title=\"AI思维过程\"\n      open={showThinking}\n      onCancel={() => setShowThinking(false)}\n      width={900}\n      footer={[\n        <Button key=\"clear\" danger onClick={() => {\n          setThinkingHistory([]);\n          setCurrentThinking('');\n          message.success('思维记录已清空');\n        }}>\n          清空记录\n        </Button>,\n        <Button key=\"close\" onClick={() => setShowThinking(false)}>\n          关闭\n        </Button>\n      ]}\n    >\n      {currentThinking && (\n        <Card\n          title=\"当前思维过程\"\n          size=\"small\"\n          style={{ marginBottom: 16 }}\n          extra={\n            <Button\n              size=\"small\"\n              icon={<CopyOutlined />}\n              onClick={() => copyToClipboard(currentThinking)}\n            >\n              复制\n            </Button>\n          }\n        >\n          <Paragraph style={{\n            backgroundColor: '#f0f8ff',\n            padding: '12px',\n            borderRadius: '6px',\n            fontFamily: 'monospace',\n            fontSize: '13px',\n            whiteSpace: 'pre-wrap'\n          }}>\n            {currentThinking}\n          </Paragraph>\n        </Card>\n      )}\n\n      <List\n        dataSource={thinkingHistory}\n        renderItem={(item) => (\n          <List.Item\n            actions={[\n              <Button\n                size=\"small\"\n                icon={<CopyOutlined />}\n                onClick={() => copyToClipboard(item.thinking)}\n              >\n                复制思维\n              </Button>,\n              <Button\n                size=\"small\"\n                icon={<CopyOutlined />}\n                onClick={() => copyToClipboard(item.content)}\n              >\n                复制结果\n              </Button>,\n              <Button\n                size=\"small\"\n                icon={<DownloadOutlined />}\n                onClick={() => saveContent(\n                  `思维过程：\\n${item.thinking}\\n\\n生成结果：\\n${item.content}`,\n                  `thinking_${item.id}.txt`\n                )}\n              >\n                下载\n              </Button>\n            ]}\n          >\n            <List.Item.Meta\n              title={\n                <Space>\n                  <Tag color=\"purple\">思维链</Tag>\n                  <Tag color=\"blue\">{item.type}</Tag>\n                  <Text type=\"secondary\">{item.timestamp}</Text>\n                </Space>\n              }\n              description={\n                <div>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>思维过程：</Text>\n                    <Paragraph\n                      ellipsis={{ rows: 3, expandable: true }}\n                      style={{\n                        backgroundColor: '#f0f8ff',\n                        padding: '8px',\n                        borderRadius: '4px',\n                        marginTop: 4,\n                        fontFamily: 'monospace',\n                        fontSize: '12px'\n                      }}\n                    >\n                      {item.thinking}\n                    </Paragraph>\n                  </div>\n                  <div>\n                    <Text strong>生成结果：</Text>\n                    <Paragraph\n                      ellipsis={{ rows: 2, expandable: true }}\n                      style={{ marginTop: 4 }}\n                    >\n                      {item.content}\n                    </Paragraph>\n                  </div>\n                </div>\n              }\n            />\n          </List.Item>\n        )}\n        locale={{ emptyText: '暂无思维记录' }}\n      />\n    </Modal>\n  );\n\n  // 聊天界面\n  const ChatInterface = () => (\n    <div style={{ height: '600px', display: 'flex', flexDirection: 'column' }}>\n      <div style={{ flex: 1, overflowY: 'auto', padding: '16px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>\n        {messages.length === 0 ? (\n          <div style={{ textAlign: 'center', color: '#999', marginTop: '100px' }}>\n            <RobotOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />\n            <div>开始与AI助手对话吧！</div>\n          </div>\n        ) : (\n          messages.map((msg, index) => (\n            <div key={index} style={{ marginBottom: '16px' }}>\n              <div style={{ display: 'flex', alignItems: 'flex-start' }}>\n                {msg.role === 'user' ? (\n                  <UserOutlined style={{ marginRight: '8px', marginTop: '4px' }} />\n                ) : (\n                  <RobotOutlined style={{ marginRight: '8px', marginTop: '4px', color: '#1890ff' }} />\n                )}\n                <div style={{ flex: 1 }}>\n                  <Text strong>{msg.role === 'user' ? '用户' : 'AI助手'}</Text>\n                  <Paragraph style={{ marginTop: '4px', marginBottom: 0 }}>\n                    {msg.content}\n                  </Paragraph>\n                  {msg.thinking && (\n                    <div style={{ marginTop: '8px' }}>\n                      <Button\n                        size=\"small\"\n                        type=\"link\"\n                        icon={<BulbOutlined />}\n                        onClick={() => {\n                          setCurrentThinking(msg.thinking);\n                          setShowThinking(true);\n                        }}\n                      >\n                        查看思维过程\n                      </Button>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n        {loading && (\n          <div style={{ textAlign: 'center', padding: '16px' }}>\n            <Spin /> <Text type=\"secondary\">AI正在思考中...</Text>\n          </div>\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      <div style={{ marginTop: '16px' }}>\n        <Input.Group compact>\n          <Input\n            style={{ width: 'calc(100% - 80px)' }}\n            placeholder=\"输入您的问题...\"\n            value={inputMessage}\n            onChange={(e) => setInputMessage(e.target.value)}\n            onPressEnter={sendMessage}\n            disabled={loading || !aiStatus.connected}\n          />\n          <Button\n            type=\"primary\"\n            icon={<SendOutlined />}\n            onClick={sendMessage}\n            loading={loading}\n            disabled={!aiStatus.connected}\n          >\n            发送\n          </Button>\n        </Input.Group>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">AI助手</Title>\n        <StatusIndicator />\n      </div>\n\n      <Row gutter={[16, 16]}>\n        <Col span={18}>\n          <Card>\n            <Tabs activeKey={activeTab} onChange={setActiveTab}>\n              <TabPane tab={<span><MessageOutlined />智能对话</span>} key=\"chat\">\n                <ChatInterface />\n              </TabPane>\n\n              <TabPane tab={<span><BulbOutlined />设定生成</span>} key=\"setting\">\n                <Form\n                  form={generateForm}\n                  layout=\"vertical\"\n                  onFinish={(values) => generateContent('setting', values, batchMode)}\n                >\n                  <Form.Item\n                    name=\"prompt\"\n                    label=\"生成要求\"\n                    rules={[{ required: true, message: '请输入生成要求' }]}\n                  >\n                    <TextArea\n                      rows={4}\n                      placeholder=\"请描述您想要生成的世界设定，例如：一个修仙世界，有多个门派...\"\n                    />\n                  </Form.Item>\n\n                  <Row gutter={16}>\n                    <Col span={12}>\n                      <Form.Item name=\"maxTokens\" label=\"最大字数\" initialValue={2000}>\n                        <Select>\n                          <Option value={1000}>1000字</Option>\n                          <Option value={2000}>2000字</Option>\n                          <Option value={3000}>3000字</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"temperature\" label=\"创意度\" initialValue={0.7}>\n                        <Select>\n                          <Option value={0.3}>保守</Option>\n                          <Option value={0.7}>平衡</Option>\n                          <Option value={0.9}>创新</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                  </Row>\n\n                  <Form.Item>\n                    <Space>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        disabled={!aiStatus.connected}\n                        icon={batchMode ? <ThunderboltOutlined /> : <BulbOutlined />}\n                      >\n                        {batchMode ? `批量生成(${batchCount}个)` : '生成世界设定'}\n                      </Button>\n                      {generatedContent && (\n                        <>\n                          <Button\n                            icon={<CopyOutlined />}\n                            onClick={() => copyToClipboard(generatedContent)}\n                          >\n                            复制\n                          </Button>\n                          <Button\n                            icon={<DownloadOutlined />}\n                            onClick={() => saveContent(generatedContent, 'setting.txt')}\n                          >\n                            下载\n                          </Button>\n                          {currentThinking && (\n                            <Button\n                              icon={<BulbOutlined />}\n                              onClick={() => setShowThinking(true)}\n                            >\n                              思维过程\n                            </Button>\n                          )}\n                        </>\n                      )}\n                    </Space>\n                  </Form.Item>\n\n                  {generatedContent && (\n                    <Form.Item label=\"生成结果\">\n                      <TextArea\n                        value={generatedContent}\n                        rows={8}\n                        readOnly\n                        style={{ backgroundColor: '#f5f5f5' }}\n                      />\n                    </Form.Item>\n                  )}\n                </Form>\n              </TabPane>\n\n              <TabPane tab={<span><UserOutlined />人物生成</span>} key=\"character\">\n                <Form\n                  layout=\"vertical\"\n                  onFinish={(values) => generateContent('character', values, batchMode)}\n                >\n                  <Form.Item\n                    name=\"prompt\"\n                    label=\"人物要求\"\n                    rules={[{ required: true, message: '请输入人物要求' }]}\n                  >\n                    <TextArea\n                      rows={4}\n                      placeholder=\"请描述您想要生成的人物，例如：一个年轻的剑修，性格冷傲...\"\n                    />\n                  </Form.Item>\n\n                  <Row gutter={16}>\n                    <Col span={12}>\n                      <Form.Item name=\"maxTokens\" label=\"最大字数\" initialValue={2000}>\n                        <Select>\n                          <Option value={1000}>1000字</Option>\n                          <Option value={2000}>2000字</Option>\n                          <Option value={3000}>3000字</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"temperature\" label=\"创意度\" initialValue={0.7}>\n                        <Select>\n                          <Option value={0.3}>保守</Option>\n                          <Option value={0.7}>平衡</Option>\n                          <Option value={0.9}>创新</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                  </Row>\n\n                  <Form.Item>\n                    <Space>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        disabled={!aiStatus.connected}\n                        icon={batchMode ? <ThunderboltOutlined /> : <UserOutlined />}\n                      >\n                        {batchMode ? `批量生成(${batchCount}个)` : '生成人物设定'}\n                      </Button>\n                      {generatedContent && (\n                        <>\n                          <Button\n                            icon={<CopyOutlined />}\n                            onClick={() => copyToClipboard(generatedContent)}\n                          >\n                            复制\n                          </Button>\n                          <Button\n                            icon={<DownloadOutlined />}\n                            onClick={() => saveContent(generatedContent, 'character.txt')}\n                          >\n                            下载\n                          </Button>\n                        </>\n                      )}\n                    </Space>\n                  </Form.Item>\n\n                  {generatedContent && (\n                    <Form.Item label=\"生成结果\">\n                      <TextArea\n                        value={generatedContent}\n                        rows={8}\n                        readOnly\n                        style={{ backgroundColor: '#f5f5f5' }}\n                      />\n                    </Form.Item>\n                  )}\n                </Form>\n              </TabPane>\n\n              <TabPane tab={<span><BookOutlined />剧情生成</span>} key=\"plot\">\n                <Form\n                  form={plotForm}\n                  layout=\"vertical\"\n                  onFinish={(values) => generateContent('plot', values, batchMode)}\n                >\n                  <Form.Item\n                    name=\"prompt\"\n                    label=\"剧情要求\"\n                    rules={[{ required: true, message: '请输入剧情要求' }]}\n                  >\n                    <TextArea\n                      rows={4}\n                      placeholder=\"请描述您想要生成的剧情，例如：主角在修仙门派中遇到的第一个挑战...\"\n                    />\n                  </Form.Item>\n\n                  <Row gutter={16}>\n                    <Col span={8}>\n                      <Form.Item name=\"maxTokens\" label=\"最大字数\" initialValue={3000}>\n                        <Select>\n                          <Option value={2000}>2000字</Option>\n                          <Option value={3000}>3000字</Option>\n                          <Option value={5000}>5000字</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                    <Col span={8}>\n                      <Form.Item name=\"temperature\" label=\"创意度\" initialValue={0.8}>\n                        <Select>\n                          <Option value={0.5}>保守</Option>\n                          <Option value={0.8}>平衡</Option>\n                          <Option value={1.0}>创新</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                    <Col span={8}>\n                      <Form.Item name=\"plotType\" label=\"剧情类型\" initialValue=\"adventure\">\n                        <Select>\n                          <Option value=\"adventure\">冒险</Option>\n                          <Option value=\"romance\">爱情</Option>\n                          <Option value=\"conflict\">冲突</Option>\n                          <Option value=\"mystery\">悬疑</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                  </Row>\n\n                  <Form.Item>\n                    <Space>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        disabled={!aiStatus.connected}\n                        icon={batchMode ? <ThunderboltOutlined /> : <BookOutlined />}\n                      >\n                        {batchMode ? `批量生成(${batchCount}个)` : '生成剧情大纲'}\n                      </Button>\n                      {generatedContent && (\n                        <>\n                          <Button\n                            icon={<CopyOutlined />}\n                            onClick={() => copyToClipboard(generatedContent)}\n                          >\n                            复制\n                          </Button>\n                          <Button\n                            icon={<DownloadOutlined />}\n                            onClick={() => saveContent(generatedContent, 'plot.txt')}\n                          >\n                            下载\n                          </Button>\n                        </>\n                      )}\n                    </Space>\n                  </Form.Item>\n\n                  {generatedContent && (\n                    <Form.Item label=\"生成结果\">\n                      <TextArea\n                        value={generatedContent}\n                        rows={8}\n                        readOnly\n                        style={{ backgroundColor: '#f5f5f5' }}\n                      />\n                    </Form.Item>\n                  )}\n                </Form>\n              </TabPane>\n\n              <TabPane tab={<span><EditOutlined />续写功能</span>} key=\"continue\">\n                <Form\n                  form={continueForm}\n                  layout=\"vertical\"\n                  onFinish={(values) => {\n                    const prompt = values.continueHint\n                      ? `${values.prompt}\\n\\n续写提示：${values.continueHint}`\n                      : values.prompt;\n                    generateContent('continue-writing', { ...values, prompt });\n                  }}\n                >\n                  <Form.Item\n                    name=\"prompt\"\n                    label=\"原文内容\"\n                    rules={[{ required: true, message: '请输入需要续写的原文内容' }]}\n                  >\n                    <TextArea\n                      rows={6}\n                      placeholder=\"请粘贴需要续写的原文内容...\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    name=\"continueHint\"\n                    label=\"续写提示（可选）\"\n                  >\n                    <TextArea\n                      rows={2}\n                      placeholder=\"可以提供续写的方向提示，例如：接下来主角遇到了...\"\n                    />\n                  </Form.Item>\n\n                  <Row gutter={16}>\n                    <Col span={12}>\n                      <Form.Item name=\"maxTokens\" label=\"续写长度\" initialValue={2000}>\n                        <Select>\n                          <Option value={1000}>短篇(1000字)</Option>\n                          <Option value={2000}>中篇(2000字)</Option>\n                          <Option value={3000}>长篇(3000字)</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"temperature\" label=\"创意度\" initialValue={0.7}>\n                        <Select>\n                          <Option value={0.5}>保守</Option>\n                          <Option value={0.7}>平衡</Option>\n                          <Option value={0.9}>创新</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                  </Row>\n\n                  <Form.Item>\n                    <Space>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        disabled={!aiStatus.connected}\n                        icon={<EditOutlined />}\n                      >\n                        AI续写\n                      </Button>\n                      {generatedContent && (\n                        <>\n                          <Button\n                            icon={<CopyOutlined />}\n                            onClick={() => copyToClipboard(generatedContent)}\n                          >\n                            复制\n                          </Button>\n                          <Button\n                            icon={<DownloadOutlined />}\n                            onClick={() => saveContent(generatedContent, 'continue.txt')}\n                          >\n                            下载\n                          </Button>\n                        </>\n                      )}\n                    </Space>\n                  </Form.Item>\n\n                  {generatedContent && (\n                    <Form.Item label=\"续写结果\">\n                      <TextArea\n                        value={generatedContent}\n                        rows={8}\n                        readOnly\n                        style={{ backgroundColor: '#f5f5f5' }}\n                      />\n                    </Form.Item>\n                  )}\n                </Form>\n              </TabPane>\n\n              <TabPane tab={<span><CheckCircleOutlined />一致性检查</span>} key=\"check\">\n                <Form\n                  form={checkForm}\n                  layout=\"vertical\"\n                  onFinish={(values) => {\n                    const prompt = `检查类型：${values.checkType}\\n\\n内容：\\n${values.prompt}`;\n                    generateContent('check-consistency', { ...values, prompt });\n                  }}\n                >\n                  <Form.Item\n                    name=\"prompt\"\n                    label=\"检查内容\"\n                    rules={[{ required: true, message: '请输入需要检查的内容' }]}\n                  >\n                    <TextArea\n                      rows={8}\n                      placeholder=\"请粘贴需要进行一致性检查的小说内容...\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    name=\"checkType\"\n                    label=\"检查类型\"\n                    initialValue=\"all\"\n                  >\n                    <Select>\n                      <Option value=\"all\">全面检查</Option>\n                      <Option value=\"character\">人物一致性</Option>\n                      <Option value=\"plot\">情节逻辑</Option>\n                      <Option value=\"setting\">设定一致性</Option>\n                      <Option value=\"timeline\">时间线</Option>\n                    </Select>\n                  </Form.Item>\n\n                  <Form.Item>\n                    <Space>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        disabled={!aiStatus.connected}\n                        icon={<CheckCircleOutlined />}\n                      >\n                        开始检查\n                      </Button>\n                      {generatedContent && (\n                        <>\n                          <Button\n                            icon={<CopyOutlined />}\n                            onClick={() => copyToClipboard(generatedContent)}\n                          >\n                            复制\n                          </Button>\n                          <Button\n                            icon={<DownloadOutlined />}\n                            onClick={() => saveContent(generatedContent, 'check_report.txt')}\n                          >\n                            下载报告\n                          </Button>\n                        </>\n                      )}\n                    </Space>\n                  </Form.Item>\n\n                  {generatedContent && (\n                    <Form.Item label=\"检查报告\">\n                      <TextArea\n                        value={generatedContent}\n                        rows={8}\n                        readOnly\n                        style={{ backgroundColor: '#f5f5f5' }}\n                      />\n                    </Form.Item>\n                  )}\n                </Form>\n              </TabPane>\n            </Tabs>\n          </Card>\n        </Col>\n\n        <Col span={6}>\n          <Card title=\"AI设置\" size=\"small\">\n            <div style={{ marginBottom: '16px' }}>\n              <Text strong>AI提供商</Text>\n              <Select\n                style={{ width: '100%', marginTop: '8px' }}\n                value={currentProvider}\n                onChange={switchProvider}\n                loading={loading}\n              >\n                {providers.map(provider => (\n                  <Option key={provider} value={provider}>\n                    {provider.toUpperCase()}\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            <Divider />\n\n            <div>\n              <Text strong>快速操作</Text>\n              <div style={{ marginTop: '8px' }}>\n                <Button\n                  block\n                  style={{ marginBottom: '8px' }}\n                  onClick={() => setMessages([])}\n                >\n                  清空对话\n                </Button>\n                <Button\n                  block\n                  onClick={fetchAIStatus}\n                  loading={loading}\n                >\n                  检查连接\n                </Button>\n              </div>\n            </div>\n          </Card>\n\n          {!aiStatus.connected && (\n            <Alert\n              style={{ marginTop: '16px' }}\n              message=\"AI服务离线\"\n              description=\"请检查AI服务配置或网络连接\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Col>\n      </Row>\n\n      {/* 新增的组件 */}\n      <AISettingsPanel />\n      <HistoryPanel />\n      <TemplatePanel />\n      <ThinkingPanel />\n    </div>\n  );\n};\n\nexport default AIAssistant;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,IAAI,EAEJC,MAAM,EACNC,MAAM,EACNC,WAAW,EAEXC,KAAK,EACLC,UAAU,EAEVC,MAAM,QACD,MAAM;AACb,SACEC,aAAa,EACbC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,mBAAmB,EAEnBC,eAAe,EAEfC,gBAAgB,EAEhBC,YAAY,EACZC,cAAc,EACdC,eAAe,EAEfC,gBAAgB,EAChBC,mBAAmB,QACd,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAG7C,UAAU;AAC7C,MAAM;EAAE8C;AAAS,CAAC,GAAG5C,KAAK;AAC1B,MAAM;EAAE6C;AAAO,CAAC,GAAG5C,MAAM;AACzB,MAAM;EAAE6C;AAAQ,CAAC,GAAG1C,IAAI;AAGxB,MAAM2C,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC;IAAE+D,SAAS,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAU,CAAC,CAAC;EACjF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqE,YAAY,CAAC,GAAG1D,IAAI,CAAC2D,OAAO,CAAC,CAAC;EACrC,MAAM,CAACC,QAAQ,CAAC,GAAG5D,IAAI,CAAC2D,OAAO,CAAC,CAAC;EACjC,MAAM,CAACE,YAAY,CAAC,GAAG7D,IAAI,CAAC2D,OAAO,CAAC,CAAC;EACrC,MAAM,CAACG,SAAS,CAAC,GAAG9D,IAAI,CAAC2D,OAAO,CAAC,CAAC;EAClC,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM4E,cAAc,GAAG1E,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM,CAAC2E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC+E,cAAc,EAAEC,iBAAiB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiF,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmF,aAAa,EAAEC,gBAAgB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuF,SAAS,EAAEC,YAAY,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC;IACvC2F,WAAW,EAAE,GAAG;IAChBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,GAAG;IACVC,iBAAiB,EAAE,GAAG;IACtBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkG,UAAU,EAAEC,aAAa,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAACoG,eAAe,EAAEC,kBAAkB,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsG,YAAY,EAAEC,eAAe,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwG,eAAe,EAAEC,kBAAkB,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM0G,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjE,KAAK,CAACkE,GAAG,CAAC,sBAAsB,CAAC;MACxDlD,YAAY,CAACiD,QAAQ,CAACE,IAAI,CAACpD,SAAS,CAAC;MACrCG,kBAAkB,CAAC+C,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;IAC3C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdnG,OAAO,CAACmG,KAAK,CAAC,aAAa,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMjE,KAAK,CAACkE,GAAG,CAAC,mBAAmB,CAAC;MACrD9C,WAAW,CAAC6C,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdjD,WAAW,CAAC;QAAEC,SAAS,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAQ,CAAC,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMiD,cAAc,GAAG,MAAOC,QAAQ,IAAK;IACzC,IAAI;MACF1D,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMd,KAAK,CAACyE,IAAI,CAAC,4BAA4B,EAAE;QAAED;MAAS,CAAC,CAAC;MAC5DtD,kBAAkB,CAACsD,QAAQ,CAAC;MAC5BtG,OAAO,CAACwG,OAAO,CAAC,QAAQF,QAAQ,EAAE,CAAC;MACnC,MAAMF,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdnG,OAAO,CAACmG,KAAK,CAAC,WAAW,CAAC;IAC5B,CAAC,SAAS;MACRvD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAClD,YAAY,CAACmD,IAAI,CAAC,CAAC,EAAE;IAE1B,MAAMC,WAAW,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEtD;IAAa,CAAC;IAC3D,MAAMuD,WAAW,GAAG,CAAC,GAAGzD,QAAQ,EAAEsD,WAAW,CAAC;IAC9CrD,WAAW,CAACwD,WAAW,CAAC;IACxBtD,eAAe,CAAC,EAAE,CAAC;IACnBZ,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMmD,QAAQ,GAAG,MAAMjE,KAAK,CAACyE,IAAI,CAAC,iBAAiB,EAAE;QACnDlD,QAAQ,EAAEyD,WAAW;QACrB,GAAGjC;MACL,CAAC,CAAC;MAEF,MAAMkC,SAAS,GAAG;QAChBH,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEd,QAAQ,CAACE,IAAI,CAACF,QAAQ;QAC/BiB,QAAQ,EAAEjB,QAAQ,CAACE,IAAI,CAACe,QAAQ;QAChCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAED7D,WAAW,CAAC,CAAC,GAAGwD,WAAW,EAAEC,SAAS,CAAC,CAAC;;MAExC;MACA,IAAIhB,QAAQ,CAACE,IAAI,CAACe,QAAQ,EAAE;QAC1BnB,kBAAkB,CAACuB,IAAI,IAAI,CAAC;UAC1BC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,CAAC;UACdT,OAAO,EAAEd,QAAQ,CAACE,IAAI,CAACF,QAAQ;UAC/BiB,QAAQ,EAAEjB,QAAQ,CAACE,IAAI,CAACe,QAAQ;UAChCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACK,cAAc,CAAC,CAAC;UACtCC,IAAI,EAAE;QACR,CAAC,EAAE,GAAGJ,IAAI,CAAC,CAAC;MACd;IAEF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MAAA,IAAAsB,eAAA;MACd,IAAI,EAAAA,eAAA,GAAAtB,KAAK,CAACJ,QAAQ,cAAA0B,eAAA,uBAAdA,eAAA,CAAgBrE,MAAM,MAAK,GAAG,EAAE;QAClCpD,OAAO,CAACmG,KAAK,CAAC,qBAAqB,CAAC;MACtC,CAAC,MAAM;QAAA,IAAAuB,gBAAA,EAAAC,qBAAA;QACL3H,OAAO,CAACmG,KAAK,CAAC,EAAAuB,gBAAA,GAAAvB,KAAK,CAACJ,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzB,IAAI,cAAA0B,qBAAA,uBAApBA,qBAAA,CAAsBC,MAAM,KAAI,QAAQ,CAAC;MACzD;IACF,CAAC,SAAS;MACRhF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiF,eAAe,GAAG,MAAAA,CAAOL,IAAI,EAAEM,MAAM,EAAEC,OAAO,GAAG,KAAK,KAAK;IAC/D,IAAI;MACFnF,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoF,MAAM,GAAG;QACbC,MAAM,EAAEH,MAAM,CAACG,MAAM;QACrBjD,UAAU,EAAE8C,MAAM,CAACI,SAAS,IAAIrD,QAAQ,CAACG,UAAU;QACnDD,WAAW,EAAE+C,MAAM,CAAC/C,WAAW,IAAIF,QAAQ,CAACE,WAAW;QACvD,GAAGF;MACL,CAAC;MAED,IAAIkD,OAAO,IAAI3C,SAAS,EAAE;QACxB;QACA,MAAM+C,OAAO,GAAG,EAAE;QAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9C,UAAU,EAAE8C,CAAC,EAAE,EAAE;UACnC,MAAMrC,QAAQ,GAAG,MAAMjE,KAAK,CAACyE,IAAI,CAAC,uBAAuBiB,IAAI,EAAE,EAAEQ,MAAM,CAAC;UACxEG,OAAO,CAACE,IAAI,CAAC;YACXhB,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,CAAC,GAAGc,CAAC;YAClBvB,OAAO,EAAEd,QAAQ,CAACE,IAAI,CAACY,OAAO;YAC9BG,QAAQ,EAAEjB,QAAQ,CAACE,IAAI,CAACe,QAAQ;YAChCQ,IAAI,EAAEA,IAAI;YACVP,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACK,cAAc,CAAC,CAAC;YACtCjB,QAAQ,EAAEvD;UACZ,CAAC,CAAC;;UAEF;UACA,IAAIgD,QAAQ,CAACE,IAAI,CAACe,QAAQ,EAAE;YAC1BnB,kBAAkB,CAACuB,IAAI,IAAI,CAAC;cAC1BC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,CAAC,GAAGc,CAAC,GAAG,IAAI;cACzBvB,OAAO,EAAEd,QAAQ,CAACE,IAAI,CAACY,OAAO;cAC9BG,QAAQ,EAAEjB,QAAQ,CAACE,IAAI,CAACe,QAAQ;cAChCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACK,cAAc,CAAC,CAAC;cACtCC,IAAI,EAAEA;YACR,CAAC,EAAE,GAAGJ,IAAI,CAAC,CAAC;UACd;QACF;;QAEA;QACAhD,iBAAiB,CAACgD,IAAI,IAAI,CAAC,GAAGe,OAAO,EAAE,GAAGf,IAAI,CAAC,CAAC;QAChDlD,mBAAmB,CAACiE,OAAO,CAAC,CAAC,CAAC,CAACtB,OAAO,CAAC;QACvCpB,kBAAkB,CAAC0C,OAAO,CAAC,CAAC,CAAC,CAACnB,QAAQ,IAAI,EAAE,CAAC;QAC7ChH,OAAO,CAACwG,OAAO,CAAC,OAAOlB,UAAU,IAAIkC,IAAI,IAAI,CAAC;QAC9C,OAAOW,OAAO,CAAC,CAAC,CAAC,CAACtB,OAAO;MAC3B,CAAC,MAAM;QACL;QACA,MAAMd,QAAQ,GAAG,MAAMjE,KAAK,CAACyE,IAAI,CAAC,uBAAuBiB,IAAI,EAAE,EAAEQ,MAAM,CAAC;QACxE,MAAMM,MAAM,GAAG;UACbjB,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,CAAC;UACdT,OAAO,EAAEd,QAAQ,CAACE,IAAI,CAACY,OAAO;UAC9BG,QAAQ,EAAEjB,QAAQ,CAACE,IAAI,CAACe,QAAQ;UAChCQ,IAAI,EAAEA,IAAI;UACVP,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACK,cAAc,CAAC,CAAC;UACtCjB,QAAQ,EAAEvD;QACZ,CAAC;;QAED;QACAqB,iBAAiB,CAACgD,IAAI,IAAI,CAACkB,MAAM,EAAE,GAAGlB,IAAI,CAAC,CAAC;QAC5ClD,mBAAmB,CAAC6B,QAAQ,CAACE,IAAI,CAACY,OAAO,CAAC;QAC1CpB,kBAAkB,CAACM,QAAQ,CAACE,IAAI,CAACe,QAAQ,IAAI,EAAE,CAAC;;QAEhD;QACA,IAAIjB,QAAQ,CAACE,IAAI,CAACe,QAAQ,EAAE;UAC1BnB,kBAAkB,CAACuB,IAAI,IAAI,CAAC;YAC1BC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,IAAI;YACrBT,OAAO,EAAEd,QAAQ,CAACE,IAAI,CAACY,OAAO;YAC9BG,QAAQ,EAAEjB,QAAQ,CAACE,IAAI,CAACe,QAAQ;YAChCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACK,cAAc,CAAC,CAAC;YACtCC,IAAI,EAAEA;UACR,CAAC,EAAE,GAAGJ,IAAI,CAAC,CAAC;QACd;QAEApH,OAAO,CAACwG,OAAO,CAAC,QAAQ,CAAC;QACzB,OAAOT,QAAQ,CAACE,IAAI,CAACY,OAAO;MAC9B;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAoC,gBAAA;MACd,IAAI,EAAAA,gBAAA,GAAApC,KAAK,CAACJ,QAAQ,cAAAwC,gBAAA,uBAAdA,gBAAA,CAAgBnF,MAAM,MAAK,GAAG,EAAE;QAClCpD,OAAO,CAACmG,KAAK,CAAC,qBAAqB,CAAC;MACtC,CAAC,MAAM;QAAA,IAAAqC,gBAAA,EAAAC,qBAAA;QACLzI,OAAO,CAACmG,KAAK,CAAC,EAAAqC,gBAAA,GAAArC,KAAK,CAACJ,QAAQ,cAAAyC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvC,IAAI,cAAAwC,qBAAA,uBAApBA,qBAAA,CAAsBb,MAAM,KAAI,KAAKJ,IAAI,IAAI,CAAC;MAC9D;MACA,OAAO,IAAI;IACb,CAAC,SAAS;MACR5E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8F,WAAW,GAAGA,CAAC7B,OAAO,EAAE8B,QAAQ,KAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAChC,OAAO,CAAC,EAAE;MAAEW,IAAI,EAAE;IAA2B,CAAC,CAAC;IACtE,MAAMsB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAGV,QAAQ,IAAI,cAAczB,IAAI,CAACI,GAAG,CAAC,CAAC,MAAM;IAC1D4B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IAC/BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;IACxB9I,OAAO,CAACwG,OAAO,CAAC,UAAU,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMmD,eAAe,GAAI9C,OAAO,IAAK;IACnC+C,SAAS,CAACC,SAAS,CAACC,SAAS,CAACjD,OAAO,CAAC,CAACkD,IAAI,CAAC,MAAM;MAChD/J,OAAO,CAACwG,OAAO,CAAC,WAAW,CAAC;IAC9B,CAAC,CAAC,CAACwD,KAAK,CAAC,MAAM;MACbhK,OAAO,CAACmG,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM8D,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,gBAAgB,GAAG,CACvB;MACE7C,EAAE,EAAE,CAAC;MACL8C,IAAI,EAAE,QAAQ;MACd3C,IAAI,EAAE,SAAS;MACfX,OAAO,EAAE;IACX,CAAC,EACD;MACEQ,EAAE,EAAE,CAAC;MACL8C,IAAI,EAAE,QAAQ;MACd3C,IAAI,EAAE,SAAS;MACfX,OAAO,EAAE;IACX,CAAC,EACD;MACEQ,EAAE,EAAE,CAAC;MACL8C,IAAI,EAAE,QAAQ;MACd3C,IAAI,EAAE,WAAW;MACjBX,OAAO,EAAE;IACX,CAAC,EACD;MACEQ,EAAE,EAAE,CAAC;MACL8C,IAAI,EAAE,QAAQ;MACd3C,IAAI,EAAE,WAAW;MACjBX,OAAO,EAAE;IACX,CAAC,CACF;IACDjC,YAAY,CAACsF,gBAAgB,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,aAAa,GAAIC,QAAQ,IAAK;IAClC,IAAIA,QAAQ,CAAC7C,IAAI,KAAK,SAAS,EAAE;MAC/B/D,YAAY,CAAC6G,cAAc,CAAC;QAAErC,MAAM,EAAEoC,QAAQ,CAACxD;MAAQ,CAAC,CAAC;MACzD9C,YAAY,CAAC,SAAS,CAAC;IACzB,CAAC,MAAM,IAAIsG,QAAQ,CAAC7C,IAAI,KAAK,WAAW,EAAE;MACxC/D,YAAY,CAAC6G,cAAc,CAAC;QAAErC,MAAM,EAAEoC,QAAQ,CAACxD;MAAQ,CAAC,CAAC;MACzD9C,YAAY,CAAC,WAAW,CAAC;IAC3B;IACAS,gBAAgB,CAAC,KAAK,CAAC;IACvBxE,OAAO,CAACwG,OAAO,CAAC,SAAS6D,QAAQ,CAACF,IAAI,EAAE,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAxG,cAAc,CAACkC,OAAO,cAAAsE,qBAAA,uBAAtBA,qBAAA,CAAwBC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDrL,SAAS,CAAC,MAAM;IACdyG,cAAc,CAAC,CAAC;IAChBM,aAAa,CAAC,CAAC;IACf6D,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN5K,SAAS,CAAC,MAAM;IACdkL,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAClH,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMsH,eAAe,GAAGA,CAAA,kBACtB3I,OAAA,CAAC9B,KAAK;IAAA0K,QAAA,gBACJ5I,OAAA,CAAC7B,GAAG;MAAC0K,KAAK,EAAE5H,QAAQ,CAACE,SAAS,GAAG,OAAO,GAAG,KAAM;MAAAyH,QAAA,EAC9C3H,QAAQ,CAACE,SAAS,GAAG,IAAI,GAAG;IAAI;MAAA2H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACNjJ,OAAA,CAACI,IAAI;MAACoF,IAAI,EAAC,WAAW;MAAAoD,QAAA,GAAC,kCAAO,EAAC7H,eAAe;IAAA;MAAA+H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACtDjJ,OAAA,CAACvC,MAAM;MACLyL,IAAI,EAAC,OAAO;MACZC,IAAI,eAAEnJ,OAAA,CAACb,cAAc;QAAA2J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACzBG,OAAO,EAAEhF,aAAc;MACvBzD,OAAO,EAAEA,OAAQ;MAAAiI,QAAA,EAClB;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;MACLyL,IAAI,EAAC,OAAO;MACZC,IAAI,eAAEnJ,OAAA,CAACT,eAAe;QAAAuJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC1BG,OAAO,EAAEA,CAAA,KAAM9G,cAAc,CAAC,IAAI,CAAE;MAAAsG,QAAA,GACrC,2BACM,eAAA5I,OAAA,CAACrB,KAAK;QAAC0K,KAAK,EAAElH,cAAc,CAACmH;MAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eACTjJ,OAAA,CAACvC,MAAM;MACLyL,IAAI,EAAC,OAAO;MACZC,IAAI,eAAEnJ,OAAA,CAACJ,gBAAgB;QAAAkJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC3BG,OAAO,EAAEA,CAAA,KAAM5G,gBAAgB,CAAC,IAAI,CAAE;MAAAoG,QAAA,EACvC;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;MACLyL,IAAI,EAAC,OAAO;MACZC,IAAI,eAAEnJ,OAAA,CAACL,eAAe;QAAAmJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC1BG,OAAO,EAAEA,CAAA,KAAM1G,eAAe,CAAC,IAAI,CAAE;MAAAkG,QAAA,EACtC;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;MACLyL,IAAI,EAAC,OAAO;MACZC,IAAI,eAAEnJ,OAAA,CAACf,YAAY;QAAA6J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACvBG,OAAO,EAAEA,CAAA,KAAMzF,eAAe,CAAC,IAAI,CAAE;MACrC6B,IAAI,EAAE5B,eAAe,CAAC0F,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAU;MAAAV,QAAA,GAC1D,2BACM,eAAA5I,OAAA,CAACrB,KAAK;QAAC0K,KAAK,EAAEzF,eAAe,CAAC0F;MAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACR;;EAED;EACA,MAAMM,eAAe,GAAGA,CAAA,kBACtBvJ,OAAA,CAACnB,MAAM;IACL2K,KAAK,EAAC,wCAAU;IAChBC,SAAS,EAAC,OAAO;IACjBC,OAAO,EAAEA,CAAA,KAAMhH,eAAe,CAAC,KAAK,CAAE;IACtCiH,IAAI,EAAElH,YAAa;IACnBmH,KAAK,EAAE,GAAI;IAAAhB,QAAA,eAEX5I,OAAA,CAACjC,IAAI;MAAC8L,MAAM,EAAC,UAAU;MAAAjB,QAAA,gBACrB5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;QAACC,KAAK,EAAC,wCAAoB;QAAAnB,QAAA,gBACnC5I,OAAA,CAACxB,MAAM;UACLwL,GAAG,EAAE,CAAE;UACPC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,GAAI;UACVC,KAAK,EAAEtH,QAAQ,CAACE,WAAY;UAC5BqH,QAAQ,EAAGD,KAAK,IAAKrH,WAAW,CAACsC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAErC,WAAW,EAAEoH;UAAM,CAAC,CAAC,CAAE;UAC5EE,KAAK,EAAE;YACL,CAAC,EAAE,IAAI;YACP,GAAG,EAAE,IAAI;YACT,GAAG,EAAE,IAAI;YACT,CAAC,EAAE;UACL;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFjJ,OAAA,CAACI,IAAI;UAACoF,IAAI,EAAC,WAAW;UAAAoD,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEZjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;QAACC,KAAK,EAAC,yBAAU;QAAAnB,QAAA,gBACzB5I,OAAA,CAACtB,WAAW;UACVsL,GAAG,EAAE,GAAI;UACTC,GAAG,EAAE,IAAK;UACVE,KAAK,EAAEtH,QAAQ,CAACG,UAAW;UAC3BoH,QAAQ,EAAGD,KAAK,IAAKrH,WAAW,CAACsC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEpC,UAAU,EAAEmH;UAAM,CAAC,CAAC,CAAE;UAC3EG,KAAK,EAAE;YAAEV,KAAK,EAAE;UAAO;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACFjJ,OAAA,CAACI,IAAI;UAACoF,IAAI,EAAC,WAAW;UAAAoD,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAEZjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;QAACC,KAAK,EAAC,OAAO;QAAAnB,QAAA,gBACtB5I,OAAA,CAACxB,MAAM;UACLwL,GAAG,EAAE,CAAE;UACPC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,GAAI;UACVC,KAAK,EAAEtH,QAAQ,CAACI,KAAM;UACtBmH,QAAQ,EAAGD,KAAK,IAAKrH,WAAW,CAACsC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEnC,KAAK,EAAEkH;UAAM,CAAC,CAAC,CAAE;UACtEE,KAAK,EAAE;YACL,CAAC,EAAE,GAAG;YACN,GAAG,EAAE,KAAK;YACV,CAAC,EAAE;UACL;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFjJ,OAAA,CAACI,IAAI;UAACoF,IAAI,EAAC,WAAW;UAAAoD,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAEZjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;QAACC,KAAK,EAAC,8CAA0B;QAAAnB,QAAA,gBACzC5I,OAAA,CAACxB,MAAM;UACLwL,GAAG,EAAE,CAAC,CAAE;UACRC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,GAAI;UACVC,KAAK,EAAEtH,QAAQ,CAACK,iBAAkB;UAClCkH,QAAQ,EAAGD,KAAK,IAAKrH,WAAW,CAACsC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAElC,iBAAiB,EAAEiH;UAAM,CAAC,CAAC,CAAE;UAClFE,KAAK,EAAE;YACL,IAAI,EAAE,IAAI;YACV,GAAG,EAAE,GAAG;YACR,GAAG,EAAE;UACP;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFjJ,OAAA,CAACI,IAAI;UAACoF,IAAI,EAAC,WAAW;UAAAoD,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAEZjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;QAACC,KAAK,EAAC,6CAAyB;QAAAnB,QAAA,gBACxC5I,OAAA,CAACxB,MAAM;UACLwL,GAAG,EAAE,CAAC,CAAE;UACRC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,GAAI;UACVC,KAAK,EAAEtH,QAAQ,CAACM,gBAAiB;UACjCiH,QAAQ,EAAGD,KAAK,IAAKrH,WAAW,CAACsC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEjC,gBAAgB,EAAEgH;UAAM,CAAC,CAAC,CAAE;UACjFE,KAAK,EAAE;YACL,IAAI,EAAE,IAAI;YACV,GAAG,EAAE,GAAG;YACR,GAAG,EAAE;UACP;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFjJ,OAAA,CAACI,IAAI;UAACoF,IAAI,EAAC,WAAW;UAAAoD,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAEZjJ,OAAA,CAAC5B,OAAO;QAAA0K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;QAACC,KAAK,EAAC,sCAAQ;QAAAnB,QAAA,eACvB5I,OAAA,CAAC9B,KAAK;UAACqM,SAAS,EAAC,UAAU;UAACD,KAAK,EAAE;YAAEV,KAAK,EAAE;UAAO,CAAE;UAAAhB,QAAA,gBACnD5I,OAAA,CAACvB,MAAM;YACL+L,OAAO,EAAEpH,SAAU;YACnBgH,QAAQ,EAAE/G,YAAa;YACvBoH,eAAe,EAAC,cAAI;YACpBC,iBAAiB,EAAC;UAAI;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EACD7F,SAAS,iBACRpD,OAAA,CAACtB,WAAW;YACVsL,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,EAAG;YACRE,KAAK,EAAE7G,UAAW;YAClB8G,QAAQ,EAAE7G,aAAc;YACxBoH,WAAW,EAAC,0BAAM;YAClBL,KAAK,EAAE;cAAEV,KAAK,EAAE;YAAO;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEZjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;QAAAlB,QAAA,eACR5I,OAAA,CAACvC,MAAM;UACL+H,IAAI,EAAC,SAAS;UACdoF,KAAK;UACLxB,OAAO,EAAEA,CAAA,KAAM;YACbpL,OAAO,CAACwG,OAAO,CAAC,SAAS,CAAC;YAC1B9B,eAAe,CAAC,KAAK,CAAC;UACxB,CAAE;UAAAkG,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CACT;;EAED;EACA,MAAM4B,YAAY,GAAGA,CAAA,kBACnB7K,OAAA,CAAC1B,KAAK;IACJkL,KAAK,EAAC,sCAAQ;IACdG,IAAI,EAAEtH,WAAY;IAClByI,QAAQ,EAAEA,CAAA,KAAMxI,cAAc,CAAC,KAAK,CAAE;IACtCsH,KAAK,EAAE,GAAI;IACXmB,MAAM,EAAE,cACN/K,OAAA,CAACvC,MAAM;MAAauN,MAAM;MAAC5B,OAAO,EAAEA,CAAA,KAAM;QACxChH,iBAAiB,CAAC,EAAE,CAAC;QACrBpE,OAAO,CAACwG,OAAO,CAAC,SAAS,CAAC;MAC5B,CAAE;MAAAoE,QAAA,EAAC;IAEH,GALY,OAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKX,CAAC,eACTjJ,OAAA,CAACvC,MAAM;MAAa2L,OAAO,EAAEA,CAAA,KAAM9G,cAAc,CAAC,KAAK,CAAE;MAAAsG,QAAA,EAAC;IAE1D,GAFY,OAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEX,CAAC,CACT;IAAAL,QAAA,eAEF5I,OAAA,CAACzB,IAAI;MACH0M,UAAU,EAAE9I,cAAe;MAC3B+I,UAAU,EAAGC,IAAI,iBACfnL,OAAA,CAACzB,IAAI,CAACuL,IAAI;QACRsB,OAAO,EAAE,cACPpL,OAAA,CAACvC,MAAM;UACLyL,IAAI,EAAC,OAAO;UACZC,IAAI,eAAEnJ,OAAA,CAACP,YAAY;YAAAqJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAACwD,IAAI,CAACtG,OAAO,CAAE;UAAA+D,QAAA,EAC9C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;UACLyL,IAAI,EAAC,OAAO;UACZC,IAAI,eAAEnJ,OAAA,CAACR,gBAAgB;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAACyE,IAAI,CAACtG,OAAO,EAAE,GAAGsG,IAAI,CAAC3F,IAAI,IAAI2F,IAAI,CAAC9F,EAAE,MAAM,CAAE;UAAAuD,QAAA,EACzE;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACpB,UAAU;UACT4K,KAAK,EAAC,8DAAY;UAClB6B,SAAS,EAAEA,CAAA,KAAM;YACfjJ,iBAAiB,CAACgD,IAAI,IAAIA,IAAI,CAACkG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClG,EAAE,KAAK8F,IAAI,CAAC9F,EAAE,CAAC,CAAC;YAC7DrH,OAAO,CAACwG,OAAO,CAAC,OAAO,CAAC;UAC1B,CAAE;UAAAoE,QAAA,eAEF5I,OAAA,CAACvC,MAAM;YAACyL,IAAI,EAAC,OAAO;YAAC8B,MAAM;YAAC7B,IAAI,eAAEnJ,OAAA,CAACN,cAAc;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,EAAC;UAEtD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,CACb;QAAAL,QAAA,eAEF5I,OAAA,CAACzB,IAAI,CAACuL,IAAI,CAAC0B,IAAI;UACbhC,KAAK,eACHxJ,OAAA,CAAC9B,KAAK;YAAA0K,QAAA,gBACJ5I,OAAA,CAAC7B,GAAG;cAAC0K,KAAK,EAAC,MAAM;cAAAD,QAAA,EAAEuC,IAAI,CAAC3F;YAAI;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnCjJ,OAAA,CAACI,IAAI;cAAAwI,QAAA,EAAEuC,IAAI,CAAClG;YAAS;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BjJ,OAAA,CAAC7B,GAAG;cAAC0K,KAAK,EAAC,OAAO;cAAAD,QAAA,EAAEuC,IAAI,CAAC7G;YAAQ;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACR;UACDwC,WAAW,eACTzL,OAAA,CAACK,SAAS;YACRqL,QAAQ,EAAE;cAAEC,IAAI,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAK,CAAE;YACxCtB,KAAK,EAAE;cAAEuB,YAAY,EAAE;YAAE,CAAE;YAAAjD,QAAA,EAE1BuC,IAAI,CAACtG;UAAO;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CACX;MACF6C,MAAM,EAAE;QAAEC,SAAS,EAAE;MAAS;IAAE;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACR;;EAED;EACA,MAAM+C,aAAa,GAAGA,CAAA,kBACpBhM,OAAA,CAAC1B,KAAK;IACJkL,KAAK,EAAC,oBAAK;IACXG,IAAI,EAAEpH,aAAc;IACpBuI,QAAQ,EAAEA,CAAA,KAAMtI,gBAAgB,CAAC,KAAK,CAAE;IACxCoH,KAAK,EAAE,GAAI;IACXmB,MAAM,EAAE,cACN/K,OAAA,CAACvC,MAAM;MAAa2L,OAAO,EAAEA,CAAA,KAAM5G,gBAAgB,CAAC,KAAK,CAAE;MAAAoG,QAAA,EAAC;IAE5D,GAFY,OAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEX,CAAC,CACT;IAAAL,QAAA,eAEF5I,OAAA,CAACzB,IAAI;MACH0M,UAAU,EAAEtI,SAAU;MACtBuI,UAAU,EAAG7C,QAAQ,iBACnBrI,OAAA,CAACzB,IAAI,CAACuL,IAAI;QACRsB,OAAO,EAAE,cACPpL,OAAA,CAACvC,MAAM;UACL+H,IAAI,EAAC,SAAS;UACd0D,IAAI,EAAC,OAAO;UACZE,OAAO,EAAEA,CAAA,KAAMhB,aAAa,CAACC,QAAQ,CAAE;UAAAO,QAAA,EACxC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,CACT;QAAAL,QAAA,eAEF5I,OAAA,CAACzB,IAAI,CAACuL,IAAI,CAAC0B,IAAI;UACbhC,KAAK,eACHxJ,OAAA,CAAC9B,KAAK;YAAA0K,QAAA,gBACJ5I,OAAA,CAACI,IAAI;cAAC6L,MAAM;cAAArD,QAAA,EAAEP,QAAQ,CAACF;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnCjJ,OAAA,CAAC7B,GAAG;cAAC0K,KAAK,EAAC,MAAM;cAAAD,QAAA,EAAEP,QAAQ,CAAC7C;YAAI;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CACR;UACDwC,WAAW,eACTzL,OAAA,CAACK,SAAS;YACRqL,QAAQ,EAAE;cAAEC,IAAI,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAK,CAAE;YACxCtB,KAAK,EAAE;cAAEuB,YAAY,EAAE;YAAE,CAAE;YAAAjD,QAAA,EAE1BP,QAAQ,CAACxD;UAAO;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IACX;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACR;;EAED;EACA,MAAMiD,aAAa,GAAGA,CAAA,kBACpBlM,OAAA,CAAC1B,KAAK;IACJkL,KAAK,EAAC,4BAAQ;IACdG,IAAI,EAAEjG,YAAa;IACnBoH,QAAQ,EAAEA,CAAA,KAAMnH,eAAe,CAAC,KAAK,CAAE;IACvCiG,KAAK,EAAE,GAAI;IACXmB,MAAM,EAAE,cACN/K,OAAA,CAACvC,MAAM;MAAauN,MAAM;MAAC5B,OAAO,EAAEA,CAAA,KAAM;QACxCvF,kBAAkB,CAAC,EAAE,CAAC;QACtBJ,kBAAkB,CAAC,EAAE,CAAC;QACtBzF,OAAO,CAACwG,OAAO,CAAC,SAAS,CAAC;MAC5B,CAAE;MAAAoE,QAAA,EAAC;IAEH,GANY,OAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMX,CAAC,eACTjJ,OAAA,CAACvC,MAAM;MAAa2L,OAAO,EAAEA,CAAA,KAAMzF,eAAe,CAAC,KAAK,CAAE;MAAAiF,QAAA,EAAC;IAE3D,GAFY,OAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEX,CAAC,CACT;IAAAL,QAAA,GAEDpF,eAAe,iBACdxD,OAAA,CAACzC,IAAI;MACHiM,KAAK,EAAC,sCAAQ;MACdN,IAAI,EAAC,OAAO;MACZoB,KAAK,EAAE;QAAEuB,YAAY,EAAE;MAAG,CAAE;MAC5BM,KAAK,eACHnM,OAAA,CAACvC,MAAM;QACLyL,IAAI,EAAC,OAAO;QACZC,IAAI,eAAEnJ,OAAA,CAACP,YAAY;UAAAqJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAACnE,eAAe,CAAE;QAAAoF,QAAA,EACjD;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;MAAAL,QAAA,eAED5I,OAAA,CAACK,SAAS;QAACiK,KAAK,EAAE;UAChB8B,eAAe,EAAE,SAAS;UAC1BC,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,WAAW;UACvBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE;QACd,CAAE;QAAA7D,QAAA,EACCpF;MAAe;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP,eAEDjJ,OAAA,CAACzB,IAAI;MACH0M,UAAU,EAAErH,eAAgB;MAC5BsH,UAAU,EAAGC,IAAI,iBACfnL,OAAA,CAACzB,IAAI,CAACuL,IAAI;QACRsB,OAAO,EAAE,cACPpL,OAAA,CAACvC,MAAM;UACLyL,IAAI,EAAC,OAAO;UACZC,IAAI,eAAEnJ,OAAA,CAACP,YAAY;YAAAqJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAACwD,IAAI,CAACnG,QAAQ,CAAE;UAAA4D,QAAA,EAC/C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;UACLyL,IAAI,EAAC,OAAO;UACZC,IAAI,eAAEnJ,OAAA,CAACP,YAAY;YAAAqJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAACwD,IAAI,CAACtG,OAAO,CAAE;UAAA+D,QAAA,EAC9C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;UACLyL,IAAI,EAAC,OAAO;UACZC,IAAI,eAAEnJ,OAAA,CAACR,gBAAgB;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CACxB,UAAUyE,IAAI,CAACnG,QAAQ,cAAcmG,IAAI,CAACtG,OAAO,EAAE,EACnD,YAAYsG,IAAI,CAAC9F,EAAE,MACrB,CAAE;UAAAuD,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,CACT;QAAAL,QAAA,eAEF5I,OAAA,CAACzB,IAAI,CAACuL,IAAI,CAAC0B,IAAI;UACbhC,KAAK,eACHxJ,OAAA,CAAC9B,KAAK;YAAA0K,QAAA,gBACJ5I,OAAA,CAAC7B,GAAG;cAAC0K,KAAK,EAAC,QAAQ;cAAAD,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7BjJ,OAAA,CAAC7B,GAAG;cAAC0K,KAAK,EAAC,MAAM;cAAAD,QAAA,EAAEuC,IAAI,CAAC3F;YAAI;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnCjJ,OAAA,CAACI,IAAI;cAACoF,IAAI,EAAC,WAAW;cAAAoD,QAAA,EAAEuC,IAAI,CAAClG;YAAS;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CACR;UACDwC,WAAW,eACTzL,OAAA;YAAA4I,QAAA,gBACE5I,OAAA;cAAKsK,KAAK,EAAE;gBAAEuB,YAAY,EAAE;cAAE,CAAE;cAAAjD,QAAA,gBAC9B5I,OAAA,CAACI,IAAI;gBAAC6L,MAAM;gBAAArD,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBjJ,OAAA,CAACK,SAAS;gBACRqL,QAAQ,EAAE;kBAAEC,IAAI,EAAE,CAAC;kBAAEC,UAAU,EAAE;gBAAK,CAAE;gBACxCtB,KAAK,EAAE;kBACL8B,eAAe,EAAE,SAAS;kBAC1BC,OAAO,EAAE,KAAK;kBACdC,YAAY,EAAE,KAAK;kBACnBI,SAAS,EAAE,CAAC;kBACZH,UAAU,EAAE,WAAW;kBACvBC,QAAQ,EAAE;gBACZ,CAAE;gBAAA5D,QAAA,EAEDuC,IAAI,CAACnG;cAAQ;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNjJ,OAAA;cAAA4I,QAAA,gBACE5I,OAAA,CAACI,IAAI;gBAAC6L,MAAM;gBAAArD,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBjJ,OAAA,CAACK,SAAS;gBACRqL,QAAQ,EAAE;kBAAEC,IAAI,EAAE,CAAC;kBAAEC,UAAU,EAAE;gBAAK,CAAE;gBACxCtB,KAAK,EAAE;kBAAEoC,SAAS,EAAE;gBAAE,CAAE;gBAAA9D,QAAA,EAEvBuC,IAAI,CAACtG;cAAO;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CACX;MACF6C,MAAM,EAAE;QAAEC,SAAS,EAAE;MAAS;IAAE;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACR;;EAED;EACA,MAAM0D,aAAa,GAAGA,CAAA,kBACpB3M,OAAA;IAAKsK,KAAK,EAAE;MAAEsC,MAAM,EAAE,OAAO;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAlE,QAAA,gBACxE5I,OAAA;MAAKsK,KAAK,EAAE;QAAEyC,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE,MAAM;QAAEX,OAAO,EAAE,MAAM;QAAEY,MAAM,EAAE,mBAAmB;QAAEX,YAAY,EAAE;MAAM,CAAE;MAAA1D,QAAA,GAC3GvH,QAAQ,CAACiI,MAAM,KAAK,CAAC,gBACpBtJ,OAAA;QAAKsK,KAAK,EAAE;UAAE4C,SAAS,EAAE,QAAQ;UAAErE,KAAK,EAAE,MAAM;UAAE6D,SAAS,EAAE;QAAQ,CAAE;QAAA9D,QAAA,gBACrE5I,OAAA,CAAClB,aAAa;UAACwL,KAAK,EAAE;YAAEkC,QAAQ,EAAE,MAAM;YAAEX,YAAY,EAAE;UAAO;QAAE;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpEjJ,OAAA;UAAA4I,QAAA,EAAK;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,GAEN5H,QAAQ,CAAC8L,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACtBrN,OAAA;QAAiBsK,KAAK,EAAE;UAAEuB,YAAY,EAAE;QAAO,CAAE;QAAAjD,QAAA,eAC/C5I,OAAA;UAAKsK,KAAK,EAAE;YAAEuC,OAAO,EAAE,MAAM;YAAES,UAAU,EAAE;UAAa,CAAE;UAAA1E,QAAA,GACvDwE,GAAG,CAACxI,IAAI,KAAK,MAAM,gBAClB5E,OAAA,CAACd,YAAY;YAACoL,KAAK,EAAE;cAAEiD,WAAW,EAAE,KAAK;cAAEb,SAAS,EAAE;YAAM;UAAE;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEjEjJ,OAAA,CAAClB,aAAa;YAACwL,KAAK,EAAE;cAAEiD,WAAW,EAAE,KAAK;cAAEb,SAAS,EAAE,KAAK;cAAE7D,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CACpF,eACDjJ,OAAA;YAAKsK,KAAK,EAAE;cAAEyC,IAAI,EAAE;YAAE,CAAE;YAAAnE,QAAA,gBACtB5I,OAAA,CAACI,IAAI;cAAC6L,MAAM;cAAArD,QAAA,EAAEwE,GAAG,CAACxI,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG;YAAM;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzDjJ,OAAA,CAACK,SAAS;cAACiK,KAAK,EAAE;gBAAEoC,SAAS,EAAE,KAAK;gBAAEb,YAAY,EAAE;cAAE,CAAE;cAAAjD,QAAA,EACrDwE,GAAG,CAACvI;YAAO;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACXmE,GAAG,CAACpI,QAAQ,iBACXhF,OAAA;cAAKsK,KAAK,EAAE;gBAAEoC,SAAS,EAAE;cAAM,CAAE;cAAA9D,QAAA,eAC/B5I,OAAA,CAACvC,MAAM;gBACLyL,IAAI,EAAC,OAAO;gBACZ1D,IAAI,EAAC,MAAM;gBACX2D,IAAI,eAAEnJ,OAAA,CAACf,YAAY;kBAAA6J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBG,OAAO,EAAEA,CAAA,KAAM;kBACb3F,kBAAkB,CAAC2J,GAAG,CAACpI,QAAQ,CAAC;kBAChCrB,eAAe,CAAC,IAAI,CAAC;gBACvB,CAAE;gBAAAiF,QAAA,EACH;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA5BEoE,KAAK;QAAAvE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6BV,CACN,CACF,EACAtI,OAAO,iBACNX,OAAA;QAAKsK,KAAK,EAAE;UAAE4C,SAAS,EAAE,QAAQ;UAAEb,OAAO,EAAE;QAAO,CAAE;QAAAzD,QAAA,gBACnD5I,OAAA,CAAC/B,IAAI;UAAA6K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,KAAC,eAAAjJ,OAAA,CAACI,IAAI;UAACoF,IAAI,EAAC,WAAW;UAAAoD,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CACN,eACDjJ,OAAA;QAAKwN,GAAG,EAAExL;MAAe;QAAA8G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAENjJ,OAAA;MAAKsK,KAAK,EAAE;QAAEoC,SAAS,EAAE;MAAO,CAAE;MAAA9D,QAAA,eAChC5I,OAAA,CAACtC,KAAK,CAAC+P,KAAK;QAACC,OAAO;QAAA9E,QAAA,gBAClB5I,OAAA,CAACtC,KAAK;UACJ4M,KAAK,EAAE;YAAEV,KAAK,EAAE;UAAoB,CAAE;UACtC+D,WAAW,EAAC,yCAAW;UACvBxD,KAAK,EAAE5I,YAAa;UACpB6I,QAAQ,EAAGwD,CAAC,IAAKpM,eAAe,CAACoM,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;UACjD2D,YAAY,EAAErJ,WAAY;UAC1BsJ,QAAQ,EAAEpN,OAAO,IAAI,CAACM,QAAQ,CAACE;QAAU;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACFjJ,OAAA,CAACvC,MAAM;UACL+H,IAAI,EAAC,SAAS;UACd2D,IAAI,eAAEnJ,OAAA,CAAChB,YAAY;YAAA8J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBG,OAAO,EAAE3E,WAAY;UACrB9D,OAAO,EAAEA,OAAQ;UACjBoN,QAAQ,EAAE,CAAC9M,QAAQ,CAACE,SAAU;UAAAyH,QAAA,EAC/B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEjJ,OAAA;IAAKgO,SAAS,EAAC,SAAS;IAAApF,QAAA,gBACtB5I,OAAA;MAAKgO,SAAS,EAAC,aAAa;MAAApF,QAAA,gBAC1B5I,OAAA,CAACG,KAAK;QAAC8N,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAApF,QAAA,EAAC;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpDjJ,OAAA,CAAC2I,eAAe;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAENjJ,OAAA,CAACpC,GAAG;MAACsQ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAtF,QAAA,gBACpB5I,OAAA,CAACnC,GAAG;QAACsQ,IAAI,EAAE,EAAG;QAAAvF,QAAA,eACZ5I,OAAA,CAACzC,IAAI;UAAAqL,QAAA,eACH5I,OAAA,CAAClC,IAAI;YAACsQ,SAAS,EAAEtM,SAAU;YAACsI,QAAQ,EAAErI,YAAa;YAAA6G,QAAA,gBACjD5I,OAAA,CAACQ,OAAO;cAAC6N,GAAG,eAAErO,OAAA;gBAAA4I,QAAA,gBAAM5I,OAAA,CAACjB,eAAe;kBAAA+J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eACjD5I,OAAA,CAAC2M,aAAa;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GADqC,MAAM;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAErD,CAAC,eAEVjJ,OAAA,CAACQ,OAAO;cAAC6N,GAAG,eAAErO,OAAA;gBAAA4I,QAAA,gBAAM5I,OAAA,CAACf,YAAY;kBAAA6J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eAC9C5I,OAAA,CAACjC,IAAI;gBACHuQ,IAAI,EAAE7M,YAAa;gBACnBoI,MAAM,EAAC,UAAU;gBACjB0E,QAAQ,EAAGzI,MAAM,IAAKD,eAAe,CAAC,SAAS,EAAEC,MAAM,EAAE1C,SAAS,CAAE;gBAAAwF,QAAA,gBAEpE5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBACR3B,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAC,0BAAM;kBACZyE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEzQ,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAA4K,QAAA,eAEhD5I,OAAA,CAACM,QAAQ;oBACPqL,IAAI,EAAE,CAAE;oBACRgC,WAAW,EAAC;kBAAkC;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZjJ,OAAA,CAACpC,GAAG;kBAACsQ,MAAM,EAAE,EAAG;kBAAAtF,QAAA,gBACd5I,OAAA,CAACnC,GAAG;oBAACsQ,IAAI,EAAE,EAAG;oBAAAvF,QAAA,eACZ5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;sBAAC3B,IAAI,EAAC,WAAW;sBAAC4B,KAAK,EAAC,0BAAM;sBAAC2E,YAAY,EAAE,IAAK;sBAAA9F,QAAA,eAC1D5I,OAAA,CAACrC,MAAM;wBAAAiL,QAAA,gBACL5I,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNjJ,OAAA,CAACnC,GAAG;oBAACsQ,IAAI,EAAE,EAAG;oBAAAvF,QAAA,eACZ5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;sBAAC3B,IAAI,EAAC,aAAa;sBAAC4B,KAAK,EAAC,oBAAK;sBAAC2E,YAAY,EAAE,GAAI;sBAAA9F,QAAA,eAC1D5I,OAAA,CAACrC,MAAM;wBAAAiL,QAAA,gBACL5I,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBAAAlB,QAAA,eACR5I,OAAA,CAAC9B,KAAK;oBAAA0K,QAAA,gBACJ5I,OAAA,CAACvC,MAAM;sBACL+H,IAAI,EAAC,SAAS;sBACdmJ,QAAQ,EAAC,QAAQ;sBACjBhO,OAAO,EAAEA,OAAQ;sBACjBoN,QAAQ,EAAE,CAAC9M,QAAQ,CAACE,SAAU;sBAC9BgI,IAAI,EAAE/F,SAAS,gBAAGpD,OAAA,CAACH,mBAAmB;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGjJ,OAAA,CAACf,YAAY;wBAAA6J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EAE5DxF,SAAS,GAAG,QAAQE,UAAU,IAAI,GAAG;oBAAQ;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,EACRhH,gBAAgB,iBACfjC,OAAA,CAAAE,SAAA;sBAAA0I,QAAA,gBACE5I,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACP,YAAY;0BAAAqJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC1F,gBAAgB,CAAE;wBAAA2G,QAAA,EAClD;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACR,gBAAgB;0BAAAsJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAACzE,gBAAgB,EAAE,aAAa,CAAE;wBAAA2G,QAAA,EAC7D;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EACRzF,eAAe,iBACdxD,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACf,YAAY;0BAAA6J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzF,eAAe,CAAC,IAAI,CAAE;wBAAAiF,QAAA,EACtC;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CACT;oBAAA,eACD,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAEXhH,gBAAgB,iBACfjC,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBAACC,KAAK,EAAC,0BAAM;kBAAAnB,QAAA,eACrB5I,OAAA,CAACM,QAAQ;oBACP6J,KAAK,EAAElI,gBAAiB;oBACxB0J,IAAI,EAAE,CAAE;oBACRiD,QAAQ;oBACRtE,KAAK,EAAE;sBAAE8B,eAAe,EAAE;oBAAU;kBAAE;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GAtF4C,SAAS;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuFrD,CAAC,eAEVjJ,OAAA,CAACQ,OAAO;cAAC6N,GAAG,eAAErO,OAAA;gBAAA4I,QAAA,gBAAM5I,OAAA,CAACd,YAAY;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eAC9C5I,OAAA,CAACjC,IAAI;gBACH8L,MAAM,EAAC,UAAU;gBACjB0E,QAAQ,EAAGzI,MAAM,IAAKD,eAAe,CAAC,WAAW,EAAEC,MAAM,EAAE1C,SAAS,CAAE;gBAAAwF,QAAA,gBAEtE5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBACR3B,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAC,0BAAM;kBACZyE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEzQ,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAA4K,QAAA,eAEhD5I,OAAA,CAACM,QAAQ;oBACPqL,IAAI,EAAE,CAAE;oBACRgC,WAAW,EAAC;kBAAgC;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZjJ,OAAA,CAACpC,GAAG;kBAACsQ,MAAM,EAAE,EAAG;kBAAAtF,QAAA,gBACd5I,OAAA,CAACnC,GAAG;oBAACsQ,IAAI,EAAE,EAAG;oBAAAvF,QAAA,eACZ5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;sBAAC3B,IAAI,EAAC,WAAW;sBAAC4B,KAAK,EAAC,0BAAM;sBAAC2E,YAAY,EAAE,IAAK;sBAAA9F,QAAA,eAC1D5I,OAAA,CAACrC,MAAM;wBAAAiL,QAAA,gBACL5I,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNjJ,OAAA,CAACnC,GAAG;oBAACsQ,IAAI,EAAE,EAAG;oBAAAvF,QAAA,eACZ5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;sBAAC3B,IAAI,EAAC,aAAa;sBAAC4B,KAAK,EAAC,oBAAK;sBAAC2E,YAAY,EAAE,GAAI;sBAAA9F,QAAA,eAC1D5I,OAAA,CAACrC,MAAM;wBAAAiL,QAAA,gBACL5I,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBAAAlB,QAAA,eACR5I,OAAA,CAAC9B,KAAK;oBAAA0K,QAAA,gBACJ5I,OAAA,CAACvC,MAAM;sBACL+H,IAAI,EAAC,SAAS;sBACdmJ,QAAQ,EAAC,QAAQ;sBACjBhO,OAAO,EAAEA,OAAQ;sBACjBoN,QAAQ,EAAE,CAAC9M,QAAQ,CAACE,SAAU;sBAC9BgI,IAAI,EAAE/F,SAAS,gBAAGpD,OAAA,CAACH,mBAAmB;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGjJ,OAAA,CAACd,YAAY;wBAAA4J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EAE5DxF,SAAS,GAAG,QAAQE,UAAU,IAAI,GAAG;oBAAQ;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,EACRhH,gBAAgB,iBACfjC,OAAA,CAAAE,SAAA;sBAAA0I,QAAA,gBACE5I,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACP,YAAY;0BAAAqJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC1F,gBAAgB,CAAE;wBAAA2G,QAAA,EAClD;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACR,gBAAgB;0BAAAsJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAACzE,gBAAgB,EAAE,eAAe,CAAE;wBAAA2G,QAAA,EAC/D;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAEXhH,gBAAgB,iBACfjC,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBAACC,KAAK,EAAC,0BAAM;kBAAAnB,QAAA,eACrB5I,OAAA,CAACM,QAAQ;oBACP6J,KAAK,EAAElI,gBAAiB;oBACxB0J,IAAI,EAAE,CAAE;oBACRiD,QAAQ;oBACRtE,KAAK,EAAE;sBAAE8B,eAAe,EAAE;oBAAU;kBAAE;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GA7E4C,WAAW;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8EvD,CAAC,eAEVjJ,OAAA,CAACQ,OAAO;cAAC6N,GAAG,eAAErO,OAAA;gBAAA4I,QAAA,gBAAM5I,OAAA,CAACZ,YAAY;kBAAA0J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eAC9C5I,OAAA,CAACjC,IAAI;gBACHuQ,IAAI,EAAE3M,QAAS;gBACfkI,MAAM,EAAC,UAAU;gBACjB0E,QAAQ,EAAGzI,MAAM,IAAKD,eAAe,CAAC,MAAM,EAAEC,MAAM,EAAE1C,SAAS,CAAE;gBAAAwF,QAAA,gBAEjE5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBACR3B,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAC,0BAAM;kBACZyE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEzQ,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAA4K,QAAA,eAEhD5I,OAAA,CAACM,QAAQ;oBACPqL,IAAI,EAAE,CAAE;oBACRgC,WAAW,EAAC;kBAAoC;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZjJ,OAAA,CAACpC,GAAG;kBAACsQ,MAAM,EAAE,EAAG;kBAAAtF,QAAA,gBACd5I,OAAA,CAACnC,GAAG;oBAACsQ,IAAI,EAAE,CAAE;oBAAAvF,QAAA,eACX5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;sBAAC3B,IAAI,EAAC,WAAW;sBAAC4B,KAAK,EAAC,0BAAM;sBAAC2E,YAAY,EAAE,IAAK;sBAAA9F,QAAA,eAC1D5I,OAAA,CAACrC,MAAM;wBAAAiL,QAAA,gBACL5I,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNjJ,OAAA,CAACnC,GAAG;oBAACsQ,IAAI,EAAE,CAAE;oBAAAvF,QAAA,eACX5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;sBAAC3B,IAAI,EAAC,aAAa;sBAAC4B,KAAK,EAAC,oBAAK;sBAAC2E,YAAY,EAAE,GAAI;sBAAA9F,QAAA,eAC1D5I,OAAA,CAACrC,MAAM;wBAAAiL,QAAA,gBACL5I,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNjJ,OAAA,CAACnC,GAAG;oBAACsQ,IAAI,EAAE,CAAE;oBAAAvF,QAAA,eACX5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;sBAAC3B,IAAI,EAAC,UAAU;sBAAC4B,KAAK,EAAC,0BAAM;sBAAC2E,YAAY,EAAC,WAAW;sBAAA9F,QAAA,eAC9D5I,OAAA,CAACrC,MAAM;wBAAAiL,QAAA,gBACL5I,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAC,WAAW;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACrCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAC,SAAS;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAC,UAAU;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAC,SAAS;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBAAAlB,QAAA,eACR5I,OAAA,CAAC9B,KAAK;oBAAA0K,QAAA,gBACJ5I,OAAA,CAACvC,MAAM;sBACL+H,IAAI,EAAC,SAAS;sBACdmJ,QAAQ,EAAC,QAAQ;sBACjBhO,OAAO,EAAEA,OAAQ;sBACjBoN,QAAQ,EAAE,CAAC9M,QAAQ,CAACE,SAAU;sBAC9BgI,IAAI,EAAE/F,SAAS,gBAAGpD,OAAA,CAACH,mBAAmB;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGjJ,OAAA,CAACZ,YAAY;wBAAA0J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EAE5DxF,SAAS,GAAG,QAAQE,UAAU,IAAI,GAAG;oBAAQ;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,EACRhH,gBAAgB,iBACfjC,OAAA,CAAAE,SAAA;sBAAA0I,QAAA,gBACE5I,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACP,YAAY;0BAAAqJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC1F,gBAAgB,CAAE;wBAAA2G,QAAA,EAClD;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACR,gBAAgB;0BAAAsJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAACzE,gBAAgB,EAAE,UAAU,CAAE;wBAAA2G,QAAA,EAC1D;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAEXhH,gBAAgB,iBACfjC,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBAACC,KAAK,EAAC,0BAAM;kBAAAnB,QAAA,eACrB5I,OAAA,CAACM,QAAQ;oBACP6J,KAAK,EAAElI,gBAAiB;oBACxB0J,IAAI,EAAE,CAAE;oBACRiD,QAAQ;oBACRtE,KAAK,EAAE;sBAAE8B,eAAe,EAAE;oBAAU;kBAAE;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GAxF4C,MAAM;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyFlD,CAAC,eAEVjJ,OAAA,CAACQ,OAAO;cAAC6N,GAAG,eAAErO,OAAA;gBAAA4I,QAAA,gBAAM5I,OAAA,CAACX,YAAY;kBAAAyJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eAC9C5I,OAAA,CAACjC,IAAI;gBACHuQ,IAAI,EAAE1M,YAAa;gBACnBiI,MAAM,EAAC,UAAU;gBACjB0E,QAAQ,EAAGzI,MAAM,IAAK;kBACpB,MAAMG,MAAM,GAAGH,MAAM,CAAC+I,YAAY,GAC9B,GAAG/I,MAAM,CAACG,MAAM,YAAYH,MAAM,CAAC+I,YAAY,EAAE,GACjD/I,MAAM,CAACG,MAAM;kBACjBJ,eAAe,CAAC,kBAAkB,EAAE;oBAAE,GAAGC,MAAM;oBAAEG;kBAAO,CAAC,CAAC;gBAC5D,CAAE;gBAAA2C,QAAA,gBAEF5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBACR3B,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAC,0BAAM;kBACZyE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEzQ,OAAO,EAAE;kBAAe,CAAC,CAAE;kBAAA4K,QAAA,eAErD5I,OAAA,CAACM,QAAQ;oBACPqL,IAAI,EAAE,CAAE;oBACRgC,WAAW,EAAC;kBAAiB;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBACR3B,IAAI,EAAC,cAAc;kBACnB4B,KAAK,EAAC,kDAAU;kBAAAnB,QAAA,eAEhB5I,OAAA,CAACM,QAAQ;oBACPqL,IAAI,EAAE,CAAE;oBACRgC,WAAW,EAAC;kBAA4B;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZjJ,OAAA,CAACpC,GAAG;kBAACsQ,MAAM,EAAE,EAAG;kBAAAtF,QAAA,gBACd5I,OAAA,CAACnC,GAAG;oBAACsQ,IAAI,EAAE,EAAG;oBAAAvF,QAAA,eACZ5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;sBAAC3B,IAAI,EAAC,WAAW;sBAAC4B,KAAK,EAAC,0BAAM;sBAAC2E,YAAY,EAAE,IAAK;sBAAA9F,QAAA,eAC1D5I,OAAA,CAACrC,MAAM;wBAAAiL,QAAA,gBACL5I,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvCjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNjJ,OAAA,CAACnC,GAAG;oBAACsQ,IAAI,EAAE,EAAG;oBAAAvF,QAAA,eACZ5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;sBAAC3B,IAAI,EAAC,aAAa;sBAAC4B,KAAK,EAAC,oBAAK;sBAAC2E,YAAY,EAAE,GAAI;sBAAA9F,QAAA,eAC1D5I,OAAA,CAACrC,MAAM;wBAAAiL,QAAA,gBACL5I,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BjJ,OAAA,CAACO,MAAM;0BAAC4J,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBAAAlB,QAAA,eACR5I,OAAA,CAAC9B,KAAK;oBAAA0K,QAAA,gBACJ5I,OAAA,CAACvC,MAAM;sBACL+H,IAAI,EAAC,SAAS;sBACdmJ,QAAQ,EAAC,QAAQ;sBACjBhO,OAAO,EAAEA,OAAQ;sBACjBoN,QAAQ,EAAE,CAAC9M,QAAQ,CAACE,SAAU;sBAC9BgI,IAAI,eAAEnJ,OAAA,CAACX,YAAY;wBAAAyJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EACxB;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRhH,gBAAgB,iBACfjC,OAAA,CAAAE,SAAA;sBAAA0I,QAAA,gBACE5I,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACP,YAAY;0BAAAqJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC1F,gBAAgB,CAAE;wBAAA2G,QAAA,EAClD;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACR,gBAAgB;0BAAAsJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAACzE,gBAAgB,EAAE,cAAc,CAAE;wBAAA2G,QAAA,EAC9D;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAEXhH,gBAAgB,iBACfjC,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBAACC,KAAK,EAAC,0BAAM;kBAAAnB,QAAA,eACrB5I,OAAA,CAACM,QAAQ;oBACP6J,KAAK,EAAElI,gBAAiB;oBACxB0J,IAAI,EAAE,CAAE;oBACRiD,QAAQ;oBACRtE,KAAK,EAAE;sBAAE8B,eAAe,EAAE;oBAAU;kBAAE;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GA7F4C,UAAU;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8FtD,CAAC,eAEVjJ,OAAA,CAACQ,OAAO;cAAC6N,GAAG,eAAErO,OAAA;gBAAA4I,QAAA,gBAAM5I,OAAA,CAACV,mBAAmB;kBAAAwJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kCAAK;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eACtD5I,OAAA,CAACjC,IAAI;gBACHuQ,IAAI,EAAEzM,SAAU;gBAChBgI,MAAM,EAAC,UAAU;gBACjB0E,QAAQ,EAAGzI,MAAM,IAAK;kBACpB,MAAMG,MAAM,GAAG,QAAQH,MAAM,CAACgJ,SAAS,YAAYhJ,MAAM,CAACG,MAAM,EAAE;kBAClEJ,eAAe,CAAC,mBAAmB,EAAE;oBAAE,GAAGC,MAAM;oBAAEG;kBAAO,CAAC,CAAC;gBAC7D,CAAE;gBAAA2C,QAAA,gBAEF5I,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBACR3B,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAC,0BAAM;kBACZyE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEzQ,OAAO,EAAE;kBAAa,CAAC,CAAE;kBAAA4K,QAAA,eAEnD5I,OAAA,CAACM,QAAQ;oBACPqL,IAAI,EAAE,CAAE;oBACRgC,WAAW,EAAC;kBAAsB;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBACR3B,IAAI,EAAC,WAAW;kBAChB4B,KAAK,EAAC,0BAAM;kBACZ2E,YAAY,EAAC,KAAK;kBAAA9F,QAAA,eAElB5I,OAAA,CAACrC,MAAM;oBAAAiL,QAAA,gBACL5I,OAAA,CAACO,MAAM;sBAAC4J,KAAK,EAAC,KAAK;sBAAAvB,QAAA,EAAC;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjCjJ,OAAA,CAACO,MAAM;sBAAC4J,KAAK,EAAC,WAAW;sBAAAvB,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCjJ,OAAA,CAACO,MAAM;sBAAC4J,KAAK,EAAC,MAAM;sBAAAvB,QAAA,EAAC;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClCjJ,OAAA,CAACO,MAAM;sBAAC4J,KAAK,EAAC,SAAS;sBAAAvB,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCjJ,OAAA,CAACO,MAAM;sBAAC4J,KAAK,EAAC,UAAU;sBAAAvB,QAAA,EAAC;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAEZjJ,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBAAAlB,QAAA,eACR5I,OAAA,CAAC9B,KAAK;oBAAA0K,QAAA,gBACJ5I,OAAA,CAACvC,MAAM;sBACL+H,IAAI,EAAC,SAAS;sBACdmJ,QAAQ,EAAC,QAAQ;sBACjBhO,OAAO,EAAEA,OAAQ;sBACjBoN,QAAQ,EAAE,CAAC9M,QAAQ,CAACE,SAAU;sBAC9BgI,IAAI,eAAEnJ,OAAA,CAACV,mBAAmB;wBAAAwJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EAC/B;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRhH,gBAAgB,iBACfjC,OAAA,CAAAE,SAAA;sBAAA0I,QAAA,gBACE5I,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACP,YAAY;0BAAAqJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC1F,gBAAgB,CAAE;wBAAA2G,QAAA,EAClD;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;wBACL0L,IAAI,eAAEnJ,OAAA,CAACR,gBAAgB;0BAAAsJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAACzE,gBAAgB,EAAE,kBAAkB,CAAE;wBAAA2G,QAAA,EAClE;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAEXhH,gBAAgB,iBACfjC,OAAA,CAACjC,IAAI,CAAC+L,IAAI;kBAACC,KAAK,EAAC,0BAAM;kBAAAnB,QAAA,eACrB5I,OAAA,CAACM,QAAQ;oBACP6J,KAAK,EAAElI,gBAAiB;oBACxB0J,IAAI,EAAE,CAAE;oBACRiD,QAAQ;oBACRtE,KAAK,EAAE;sBAAE8B,eAAe,EAAE;oBAAU;kBAAE;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GA1EoD,OAAO;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2E3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENjJ,OAAA,CAACnC,GAAG;QAACsQ,IAAI,EAAE,CAAE;QAAAvF,QAAA,gBACX5I,OAAA,CAACzC,IAAI;UAACiM,KAAK,EAAC,gBAAM;UAACN,IAAI,EAAC,OAAO;UAAAN,QAAA,gBAC7B5I,OAAA;YAAKsK,KAAK,EAAE;cAAEuB,YAAY,EAAE;YAAO,CAAE;YAAAjD,QAAA,gBACnC5I,OAAA,CAACI,IAAI;cAAC6L,MAAM;cAAArD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBjJ,OAAA,CAACrC,MAAM;cACL2M,KAAK,EAAE;gBAAEV,KAAK,EAAE,MAAM;gBAAE8C,SAAS,EAAE;cAAM,CAAE;cAC3CvC,KAAK,EAAEpJ,eAAgB;cACvBqJ,QAAQ,EAAE/F,cAAe;cACzB1D,OAAO,EAAEA,OAAQ;cAAAiI,QAAA,EAEhB/H,SAAS,CAACsM,GAAG,CAAC7I,QAAQ,iBACrBtE,OAAA,CAACO,MAAM;gBAAgB4J,KAAK,EAAE7F,QAAS;gBAAAsE,QAAA,EACpCtE,QAAQ,CAACyK,WAAW,CAAC;cAAC,GADZzK,QAAQ;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEb,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENjJ,OAAA,CAAC5B,OAAO;YAAA0K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEXjJ,OAAA;YAAA4I,QAAA,gBACE5I,OAAA,CAACI,IAAI;cAAC6L,MAAM;cAAArD,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxBjJ,OAAA;cAAKsK,KAAK,EAAE;gBAAEoC,SAAS,EAAE;cAAM,CAAE;cAAA9D,QAAA,gBAC/B5I,OAAA,CAACvC,MAAM;gBACLmN,KAAK;gBACLN,KAAK,EAAE;kBAAEuB,YAAY,EAAE;gBAAM,CAAE;gBAC/BzC,OAAO,EAAEA,CAAA,KAAM9H,WAAW,CAAC,EAAE,CAAE;gBAAAsH,QAAA,EAChC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACvC,MAAM;gBACLmN,KAAK;gBACLxB,OAAO,EAAEhF,aAAc;gBACvBzD,OAAO,EAAEA,OAAQ;gBAAAiI,QAAA,EAClB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAEN,CAAChI,QAAQ,CAACE,SAAS,iBAClBnB,OAAA,CAAC3B,KAAK;UACJiM,KAAK,EAAE;YAAEoC,SAAS,EAAE;UAAO,CAAE;UAC7B1O,OAAO,EAAC,4BAAQ;UAChByN,WAAW,EAAC,4EAAgB;UAC5BjG,IAAI,EAAC,SAAS;UACdwJ,QAAQ;QAAA;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjJ,OAAA,CAACuJ,eAAe;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBjJ,OAAA,CAAC6K,YAAY;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBjJ,OAAA,CAACgM,aAAa;MAAAlD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjBjJ,OAAA,CAACkM,aAAa;MAAApD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;AAACvI,EAAA,CA7wCID,WAAW;EAAA,QAOQ1C,IAAI,CAAC2D,OAAO,EAChB3D,IAAI,CAAC2D,OAAO,EACR3D,IAAI,CAAC2D,OAAO,EACf3D,IAAI,CAAC2D,OAAO;AAAA;AAAAuN,EAAA,GAV5BxO,WAAW;AA+wCjB,eAAeA,WAAW;AAAC,IAAAwO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}