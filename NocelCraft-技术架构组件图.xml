<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1705.6640625 1318" style="max-width: 1705.6640625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1"><style>#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .error-icon{fill:#a44141;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .edge-thickness-normal{stroke-width:1px;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .marker.cross{stroke:lightgrey;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 p{margin:0;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .cluster-label text{fill:#F9FFFE;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .cluster-label span{color:#F9FFFE;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .cluster-label span p{background-color:transparent;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .label text,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 span{fill:#ccc;color:#ccc;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .node rect,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .node circle,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .node ellipse,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .node polygon,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .rough-node .label text,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .node .label text,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .image-shape .label,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .icon-shape .label{text-anchor:middle;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .rough-node .label,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .node .label,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .image-shape .label,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .icon-shape .label{text-align:center;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .node.clickable{cursor:pointer;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .arrowheadPath{fill:lightgrey;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .cluster text{fill:#F9FFFE;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .cluster span{color:#F9FFFE;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 rect.text{fill:none;stroke-width:0;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .icon-shape,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .icon-shape p,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .icon-shape rect,#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .frontendClass&gt;*{fill:#e3f2fd!important;stroke:#1565c0!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .frontendClass span{fill:#e3f2fd!important;stroke:#1565c0!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .backendClass&gt;*{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .backendClass span{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .aiClass&gt;*{fill:#fff3e0!important;stroke:#ef6c00!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .aiClass span{fill:#fff3e0!important;stroke:#ef6c00!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .convClass&gt;*{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .convClass span{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .dataClass&gt;*{fill:#e0f2f1!important;stroke:#00695c!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .dataClass span{fill:#e0f2f1!important;stroke:#00695c!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .secClass&gt;*{fill:#ffebee!important;stroke:#c62828!important;stroke-width:2px!important;}#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1 .secClass span{fill:#ffebee!important;stroke:#c62828!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph5" class="cluster"><rect height="843" width="214.0208282470703" y="314" x="8" style=""></rect><g transform="translate(15.010414123535156, 314)" class="cluster-label"><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>安全与监控技术栈 (Security &amp; Monitoring Stack)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="843" width="226.9791717529297" y="314" x="488.0208282470703" style=""></rect><g transform="translate(501.51041412353516, 314)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据处理技术栈 (Data Processing Stack)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="818" width="226" y="492" x="242.0208282470703" style=""></rect><g transform="translate(255.0208282470703, 492)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>对话引擎技术栈 (Conversation Engine Stack)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="843" width="243.47395706176758" y="314" x="735" style=""></rect><g transform="translate(756.7369785308838, 314)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AI引擎技术栈 (AI Engine Stack)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="843" width="236.3828125" y="161" x="998.4739570617676" style=""></rect><g transform="translate(1016.8320274353027, 161)" class="cluster-label"><foreignObject height="24" width="199.6666717529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>后端技术栈 (Backend Stack)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="434" width="442.80728912353516" y="8" x="1254.8567695617676" style=""></rect><g transform="translate(1376.2604141235352, 8)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>前端技术栈 (Frontend Stack)</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FE1_BE1_0" d="M1432.529,111L1438.583,115.167C1444.638,119.333,1456.747,127.667,1462.802,136C1468.857,144.333,1468.857,152.667,1421.418,165.221C1373.98,177.775,1279.102,194.55,1231.664,202.937L1184.225,211.325"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FE2_FE3_1" d="M1370.453,264L1370.453,268.167C1370.453,272.333,1370.453,280.667,1370.453,289C1370.453,297.333,1370.453,305.667,1370.453,313.333C1370.453,321,1370.453,328,1370.453,331.5L1370.453,335"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FE4_FE5_2" d="M1581.857,111L1581.857,115.167C1581.857,119.333,1581.857,127.667,1581.857,136C1581.857,144.333,1581.857,152.667,1581.857,160.333C1581.857,168,1581.857,175,1581.857,178.5L1581.857,182"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FE5_FE6_3" d="M1581.857,264L1581.857,268.167C1581.857,272.333,1581.857,280.667,1581.857,289C1581.857,297.333,1581.857,305.667,1581.857,313.333C1581.857,321,1581.857,328,1581.857,331.5L1581.857,335"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BE1_BE2_4" d="M1119.33,264L1120.66,268.167C1121.99,272.333,1124.65,280.667,1125.98,289C1127.31,297.333,1127.31,305.667,1127.31,313.333C1127.31,321,1127.31,328,1127.31,331.5L1127.31,335"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BE2_BE3_5" d="M1127.31,417L1127.31,421.167C1127.31,425.333,1127.31,433.667,1127.31,442C1127.31,450.333,1127.31,458.667,1127.31,467C1127.31,475.333,1127.31,483.667,1127.31,491.333C1127.31,499,1127.31,506,1127.31,509.5L1127.31,513"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BE3_BE4_6" d="M1127.31,595L1127.31,599.167C1127.31,603.333,1127.31,611.667,1127.31,619.333C1127.31,627,1127.31,634,1127.31,637.5L1127.31,641"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BE4_BE5_7" d="M1127.31,723L1127.31,727.167C1127.31,731.333,1127.31,739.667,1127.31,747.333C1127.31,755,1127.31,762,1127.31,765.5L1127.31,769"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BE5_BE6_8" d="M1127.31,851L1127.31,855.167C1127.31,859.333,1127.31,867.667,1127.31,875.333C1127.31,883,1127.31,890,1127.31,893.5L1127.31,897"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AI1_AI2_9" d="M850.237,417L851.443,421.167C852.649,425.333,855.062,433.667,856.268,442C857.474,450.333,857.474,458.667,857.474,467C857.474,475.333,857.474,483.667,857.474,491.333C857.474,499,857.474,506,857.474,509.5L857.474,513"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AI2_AI3_10" d="M857.474,595L857.474,599.167C857.474,603.333,857.474,611.667,857.474,619.333C857.474,627,857.474,634,857.474,637.5L857.474,641"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AI3_AI4_11" d="M857.474,723L857.474,727.167C857.474,731.333,857.474,739.667,857.474,747.333C857.474,755,857.474,762,857.474,765.5L857.474,769"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AI4_AI5_12" d="M857.474,851L857.474,855.167C857.474,859.333,857.474,867.667,857.474,875.333C857.474,883,857.474,890,857.474,893.5L857.474,897"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AI5_AI6_13" d="M857.474,979L857.474,983.167C857.474,987.333,857.474,995.667,857.474,1004C857.474,1012.333,857.474,1020.667,857.474,1028.333C857.474,1036,857.474,1043,857.474,1046.5L857.474,1050"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CONV1_CONV2_14" d="M355.021,595L355.021,599.167C355.021,603.333,355.021,611.667,355.021,619.333C355.021,627,355.021,634,355.021,637.5L355.021,641"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CONV2_CONV3_15" d="M355.021,723L355.021,727.167C355.021,731.333,355.021,739.667,355.021,747.333C355.021,755,355.021,762,355.021,765.5L355.021,769"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CONV3_CONV4_16" d="M355.021,851L355.021,855.167C355.021,859.333,355.021,867.667,355.021,875.333C355.021,883,355.021,890,355.021,893.5L355.021,897"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CONV4_CONV5_17" d="M355.021,979L355.021,983.167C355.021,987.333,355.021,995.667,355.021,1004C355.021,1012.333,355.021,1020.667,355.021,1028.333C355.021,1036,355.021,1043,355.021,1046.5L355.021,1050"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CONV5_CONV6_18" d="M355.021,1132L355.021,1136.167C355.021,1140.333,355.021,1148.667,355.021,1157C355.021,1165.333,355.021,1173.667,355.021,1181.333C355.021,1189,355.021,1196,355.021,1199.5L355.021,1203"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DATA1_DATA2_19" d="M601.51,417L601.51,421.167C601.51,425.333,601.51,433.667,601.51,442C601.51,450.333,601.51,458.667,601.51,467C601.51,475.333,601.51,483.667,601.51,491.333C601.51,499,601.51,506,601.51,509.5L601.51,513"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DATA2_DATA3_20" d="M601.51,595L601.51,599.167C601.51,603.333,601.51,611.667,601.51,619.333C601.51,627,601.51,634,601.51,637.5L601.51,641"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DATA3_DATA4_21" d="M601.51,723L601.51,727.167C601.51,731.333,601.51,739.667,601.51,747.333C601.51,755,601.51,762,601.51,765.5L601.51,769"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DATA4_DATA5_22" d="M601.51,851L601.51,855.167C601.51,859.333,601.51,867.667,601.51,875.333C601.51,883,601.51,890,601.51,893.5L601.51,897"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DATA5_DATA6_23" d="M601.51,979L601.51,983.167C601.51,987.333,601.51,995.667,601.51,1004C601.51,1012.333,601.51,1020.667,601.51,1028.333C601.51,1036,601.51,1043,601.51,1046.5L601.51,1050"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SEC1_SEC2_24" d="M115.01,417L115.01,421.167C115.01,425.333,115.01,433.667,115.01,442C115.01,450.333,115.01,458.667,115.01,467C115.01,475.333,115.01,483.667,115.01,491.333C115.01,499,115.01,506,115.01,509.5L115.01,513"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SEC2_SEC3_25" d="M115.01,595L115.01,599.167C115.01,603.333,115.01,611.667,115.01,619.333C115.01,627,115.01,634,115.01,637.5L115.01,641"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SEC3_SEC4_26" d="M115.01,723L115.01,727.167C115.01,731.333,115.01,739.667,115.01,747.333C115.01,755,115.01,762,115.01,765.5L115.01,769"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SEC4_SEC5_27" d="M115.01,851L115.01,855.167C115.01,859.333,115.01,867.667,115.01,875.333C115.01,883,115.01,890,115.01,893.5L115.01,897"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SEC5_SEC6_28" d="M115.01,979L115.01,983.167C115.01,987.333,115.01,995.667,115.01,1004C115.01,1012.333,115.01,1020.667,115.01,1028.333C115.01,1036,115.01,1043,115.01,1046.5L115.01,1050"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FE1_BE1_29" d="M1345.083,111L1341.796,115.167C1338.508,119.333,1331.932,127.667,1328.645,136C1325.357,144.333,1325.357,152.667,1301.818,163.729C1278.28,174.791,1231.202,188.581,1207.664,195.477L1184.125,202.372"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BE1_AI1_30" d="M1107.142,264L1107.17,268.167C1107.198,272.333,1107.254,280.667,1107.282,289C1107.31,297.333,1107.31,305.667,1074.723,317.605C1042.136,329.543,976.961,345.086,944.374,352.858L911.787,360.629"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AI1_CONV1_31" d="M813.37,417L810.637,421.167C807.905,425.333,802.439,433.667,799.707,442C796.974,450.333,796.974,458.667,796.974,467C796.974,475.333,796.974,483.667,736.975,496.522C676.976,509.377,556.978,526.754,496.979,535.443L436.98,544.131"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BE1_DATA1_32" d="M1086.038,264L1083.811,268.167C1081.584,272.333,1077.131,280.667,1074.904,289C1072.677,297.333,1072.677,305.667,1005.143,319.007C937.609,332.347,802.542,350.693,735.008,359.867L667.474,369.04"></path><path marker-end="url(#mermaid-aa3adc20-fb91-425d-a65f-e46a4e8785b1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BE1_SEC1_33" d="M1073.85,264L1070.321,268.167C1066.792,272.333,1059.735,280.667,1056.206,289C1052.677,297.333,1052.677,305.667,907.398,319.749C762.118,333.832,471.56,353.664,326.28,363.58L181.001,373.496"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1375.8567695617676, 72)" id="flowchart-FE1-391" class="node default frontendClass"><rect height="78" width="172" y="-39" x="-86" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-56, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Electron<br>跨平台桌面应用</p></span></div></foreignObject></g></g><g transform="translate(1370.453125, 225)" id="flowchart-FE2-392" class="node default frontendClass"><rect height="78" width="160.28125" y="-39" x="-80.140625" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-50.140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="100.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>React + Hooks<br>用户界面框架</p></span></div></foreignObject></g></g><g transform="translate(1370.453125, 378)" id="flowchart-FE3-393" class="node default frontendClass"><rect height="78" width="135.6979217529297" y="-39" x="-67.84896087646484" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-37.848960876464844, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="75.69792175292969"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Ant Design<br>UI组件库</p></span></div></foreignObject></g></g><g transform="translate(1581.8567695617676, 72)" id="flowchart-FE4-394" class="node default frontendClass"><rect height="78" width="140" y="-39" x="-70" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-40, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>D3.js<br>数据可视化</p></span></div></foreignObject></g></g><g transform="translate(1581.8567695617676, 225)" id="flowchart-FE5-395" class="node default frontendClass"><rect height="78" width="161.61458587646484" y="-39" x="-80.80729293823242" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-50.80729293823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="101.61458587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Monaco Editor<br>代码编辑器</p></span></div></foreignObject></g></g><g transform="translate(1581.8567695617676, 378)" id="flowchart-FE6-396" class="node default frontendClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Slate.js<br>富文本编辑器</p></span></div></foreignObject></g></g><g transform="translate(1106.8802070617676, 225)" id="flowchart-BE1-397" class="node default backendClass"><rect height="78" width="146.8125" y="-39" x="-73.40625" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-43.40625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="86.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FastAPI<br>API服务框架</p></span></div></foreignObject></g></g><g transform="translate(1127.3098945617676, 378)" id="flowchart-BE2-398" class="node default backendClass"><rect height="78" width="145.09375" y="-39" x="-72.546875" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-42.546875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="85.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Python 3.9+<br>核心语言</p></span></div></foreignObject></g></g><g transform="translate(1127.3098945617676, 556)" id="flowchart-BE3-399" class="node default backendClass"><rect height="78" width="140" y="-39" x="-70" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-40, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SQLite<br>本地数据库</p></span></div></foreignObject></g></g><g transform="translate(1127.3098945617676, 684)" id="flowchart-BE4-400" class="node default backendClass"><rect height="78" width="140" y="-39" x="-70" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-40, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MongoDB<br>云端数据库</p></span></div></foreignObject></g></g><g transform="translate(1127.3098945617676, 812)" id="flowchart-BE5-401" class="node default backendClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis<br>缓存系统</p></span></div></foreignObject></g></g><g transform="translate(1127.3098945617676, 940)" id="flowchart-BE6-402" class="node default backendClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Celery<br>任务队列</p></span></div></foreignObject></g></g><g transform="translate(838.9479141235352, 378)" id="flowchart-AI1-403" class="node default aiClass"><rect height="78" width="137.89583587646484" y="-39" x="-68.94791793823242" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-38.94791793823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="77.89583587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LangChain<br>AI应用框架</p></span></div></foreignObject></g></g><g transform="translate(857.4739570617676, 556)" id="flowchart-AI2-404" class="node default aiClass"><rect height="78" width="152.80208587646484" y="-39" x="-76.40104293823242" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-46.40104293823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="92.80208587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Transformers<br>模型库</p></span></div></foreignObject></g></g><g transform="translate(857.4739570617676, 684)" id="flowchart-AI3-405" class="node default aiClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RWKV<br>本地写作模型</p></span></div></foreignObject></g></g><g transform="translate(857.4739570617676, 812)" id="flowchart-AI4-406" class="node default aiClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Ollama<br>本地模型服务</p></span></div></foreignObject></g></g><g transform="translate(857.4739570617676, 940)" id="flowchart-AI5-407" class="node default aiClass"><rect height="78" width="137.89583587646484" y="-39" x="-68.94791793823242" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-38.94791793823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="77.89583587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>OpenAI API<br>云端AI服务</p></span></div></foreignObject></g></g><g transform="translate(857.4739570617676, 1093)" id="flowchart-AI6-408" class="node default aiClass"><rect height="78" width="172" y="-39" x="-86" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-56, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>自定义微调模型<br>专业化AI</p></span></div></foreignObject></g></g><g transform="translate(355.0208282470703, 556)" id="flowchart-CONV1-409" class="node default convClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>spaCy<br>自然语言处理</p></span></div></foreignObject></g></g><g transform="translate(355.0208282470703, 684)" id="flowchart-CONV2-410" class="node default convClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>NLTK<br>文本分析</p></span></div></foreignObject></g></g><g transform="translate(355.0208282470703, 812)" id="flowchart-CONV3-411" class="node default convClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>状态机<br>对话流程管理</p></span></div></foreignObject></g></g><g transform="translate(355.0208282470703, 940)" id="flowchart-CONV4-412" class="node default convClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>上下文管理器<br>记忆机制</p></span></div></foreignObject></g></g><g transform="translate(355.0208282470703, 1093)" id="flowchart-CONV5-413" class="node default convClass"><rect height="78" width="140" y="-39" x="-70" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-40, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>意图识别器<br>语义理解</p></span></div></foreignObject></g></g><g transform="translate(355.0208282470703, 1246)" id="flowchart-CONV6-414" class="node default convClass"><rect height="78" width="140" y="-39" x="-70" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-40, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>问题生成器<br>智能提问</p></span></div></foreignObject></g></g><g transform="translate(601.5104141235352, 378)" id="flowchart-DATA1-415" class="node default dataClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#e0f2f1 !important;stroke:#00695c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Pandas<br>数据分析</p></span></div></foreignObject></g></g><g transform="translate(601.5104141235352, 556)" id="flowchart-DATA2-416" class="node default dataClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#e0f2f1 !important;stroke:#00695c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>NumPy<br>数值计算</p></span></div></foreignObject></g></g><g transform="translate(601.5104141235352, 684)" id="flowchart-DATA3-417" class="node default dataClass"><rect height="78" width="128.96875" y="-39" x="-64.484375" style="fill:#e0f2f1 !important;stroke:#00695c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-34.484375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="68.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>NetworkX<br>关系图谱</p></span></div></foreignObject></g></g><g transform="translate(601.5104141235352, 812)" id="flowchart-DATA4-418" class="node default dataClass"><rect height="78" width="153.5" y="-39" x="-76.75" style="fill:#e0f2f1 !important;stroke:#00695c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-46.75, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="93.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Elasticsearch<br>全文搜索</p></span></div></foreignObject></g></g><g transform="translate(601.5104141235352, 940)" id="flowchart-DATA5-419" class="node default dataClass"><rect height="78" width="156.9791717529297" y="-39" x="-78.48958587646484" style="fill:#e0f2f1 !important;stroke:#00695c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48.489585876464844, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96.97917175292969"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Apache Kafka<br>消息队列</p></span></div></foreignObject></g></g><g transform="translate(601.5104141235352, 1093)" id="flowchart-DATA6-420" class="node default dataClass"><rect height="78" width="140" y="-39" x="-70" style="fill:#e0f2f1 !important;stroke:#00695c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-40, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Docker<br>容器化部署</p></span></div></foreignObject></g></g><g transform="translate(115.01041412353516, 378)" id="flowchart-SEC1-421" class="node default secClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#ffebee !important;stroke:#c62828 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>JWT<br>身份认证</p></span></div></foreignObject></g></g><g transform="translate(115.01041412353516, 556)" id="flowchart-SEC2-422" class="node default secClass"><rect height="78" width="131.53125" y="-39" x="-65.765625" style="fill:#ffebee !important;stroke:#c62828 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-35.765625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="71.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>OAuth 2.0<br>授权协议</p></span></div></foreignObject></g></g><g transform="translate(115.01041412353516, 684)" id="flowchart-SEC3-423" class="node default secClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#ffebee !important;stroke:#c62828 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AES加密<br>数据安全</p></span></div></foreignObject></g></g><g transform="translate(115.01041412353516, 812)" id="flowchart-SEC4-424" class="node default secClass"><rect height="78" width="144.02083587646484" y="-39" x="-72.01041793823242" style="fill:#ffebee !important;stroke:#c62828 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-42.01041793823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="84.02083587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Prometheus<br>系统监控</p></span></div></foreignObject></g></g><g transform="translate(115.01041412353516, 940)" id="flowchart-SEC5-425" class="node default secClass"><rect height="78" width="140" y="-39" x="-70" style="fill:#ffebee !important;stroke:#c62828 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-40, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Grafana<br>数据可视化</p></span></div></foreignObject></g></g><g transform="translate(115.01041412353516, 1093)" id="flowchart-SEC6-426" class="node default secClass"><rect height="78" width="129.14583587646484" y="-39" x="-64.57291793823242" style="fill:#ffebee !important;stroke:#c62828 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-34.57291793823242, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="69.14583587646484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ELK Stack<br>日志分析</p></span></div></foreignObject></g></g></g></g></g></svg>